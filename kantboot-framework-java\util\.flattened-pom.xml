<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>dependencies</artifactId>
    <version>ARISTOTLE-0.1</version>
    <relativePath>../dependencies/pom.xml</relativePath>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>util</artifactId>
  <version>ARISTOTLE-0.1</version>
  <packaging>pom</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>util-event</module>
    <module>util-http</module>
    <module>util-cache</module>
    <module>util-rest</module>
    <module>util-jpa</module>
    <module>util-crypto</module>
    <module>util-sc</module>
    <module>util-data-change</module>
    <module>util-all</module>
    <module>util-auth</module>
    <module>util-file</module>
    <module>util-setting</module>
    <module>util-i18n</module>
    <module>util-qrcode</module>
  </modules>
</project>
