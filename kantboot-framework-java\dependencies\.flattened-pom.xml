<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>parent</artifactId>
    <version>ARISTOTLE-0.1</version>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>dependencies</artifactId>
  <version>ARISTOTLE-0.1</version>
  <packaging>pom</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <javax.mail.version>1.6.2</javax.mail.version>
    <okhttp3.version>4.12.0</okhttp3.version>
    <yitter-idgenerator.version>1.0.6</yitter-idgenerator.version>
    <dubbo.version>3.3.3</dubbo.version>
    <zxing.version>3.5.1</zxing.version>
    <hutool.version>5.8.24</hutool.version>
    <fastjson2.version>2.0.37</fastjson2.version>
    <reflections.version>0.9.12</reflections.version>
    <lombok.version>1.18.34</lombok.version>
    <slf4j.version>2.0.16</slf4j.version>
    <postgresql.version>42.7.2</postgresql.version>
    <druid.version>1.2.6</druid.version>
    <fastjson2-extension.version>2.0.9.graal</fastjson2-extension.version>
    <mysql.version>8.0.33</mysql.version>
    <caffeine.version>2.8.5</caffeine.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.mail</groupId>
        <artifactId>javax.mail-api</artifactId>
        <version>${javax.mail.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sun.mail</groupId>
        <artifactId>javax.mail</artifactId>
        <version>${javax.mail.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2</artifactId>
        <version>${fastjson2.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2-extension</artifactId>
        <version>${fastjson2-extension.version}</version>
      </dependency>
      <dependency>
        <groupId>org.reflections</groupId>
        <artifactId>reflections</artifactId>
        <version>${reflections.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.yitter</groupId>
        <artifactId>yitter-idgenerator</artifactId>
        <version>${yitter-idgenerator.version}</version>
      </dependency>
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql.version}</version>
        <scope>runtime</scope>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>${druid.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
        <version>${spring-boot.version}</version>
      </dependency>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>${postgresql.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>caffeine</artifactId>
        <version>${caffeine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.zxing</groupId>
        <artifactId>core</artifactId>
        <version>${zxing.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
