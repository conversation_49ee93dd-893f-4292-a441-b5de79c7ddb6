<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>dependencies</artifactId>
    <version>ARISTOTLE-0.1</version>
    <relativePath>../dependencies/pom.xml</relativePath>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>api</artifactId>
  <version>ARISTOTLE-0.1</version>
  <packaging>pom</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>api-baidu</module>
    <module>api-google</module>
    <module>api-qiniucloud</module>
    <module>api-twilio</module>
    <module>api-alicloud</module>
    <module>api-wechat</module>
    <module>api-paypal</module>
  </modules>
</project>
