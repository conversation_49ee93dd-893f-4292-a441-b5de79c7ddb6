E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\user\user-location\src\main\java\com\kantboot\user\location\dao\repository\UserAccountLocationLogRepository.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\user\user-location\src\main\java\com\kantboot\user\location\dao\repository\UserAccountLocationRepository.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\user\user-location\src\main\java\com\kantboot\user\location\domain\entity\UserAccountLocation.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\user\user-location\src\main\java\com\kantboot\user\location\domain\entity\UserAccountLocationLog.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\user\user-location\src\main\java\com\kantboot\user\location\service\impl\UserAccountLocationServiceImpl.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\user\user-location\src\main\java\com\kantboot\user\location\service\IUserAccountLocationService.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\user\user-location\src\main\java\com\kantboot\user\location\web\controller\UserAccountLocationController.java
