E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-translate\src\main\java\com\kantboot\functional\translate\entity\FunctionalTranslateText.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-translate\src\main\java\com\kantboot\functional\translate\repository\FunctionalTranslateTextRepository.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-translate\src\main\java\com\kantboot\functional\translate\service\IFunctionalTranslateService.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-translate\src\main\java\com\kantboot\functional\translate\service\impl\FunctionalTranslateServiceImpl.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-translate\src\main\java\com\kantboot\functional\translate\slot\FunctionalTranslateSlot.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-translate\src\main\java\com\kantboot\functional\translate\web\controller\FunctionalTranslateController.java
