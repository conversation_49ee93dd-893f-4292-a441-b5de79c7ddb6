package com.kantboot.project.make.friends.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 邀约协作组包含创建人信息的DTO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProjectMakeFriendsInvitationGroupWithCreatorDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 最后一次修改时间
     */
    private Date gmtModified;

    /**
     * 邀约协作组名称
     */
    private String groupName;

    /**
     * 协议人ID集合，使用逗号隔开
     */
    private String agreementUserIds;

    /**
     * 创建者用户账号ID
     */
    private Long creatorUserAccountId;

    /**
     * 创建确认状态：0-待所有人确认，1-已确认创建
     */
    private Integer creationConfirmStatus;

    /**
     * 邀约描述
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人昵称
     */
    private String creatorNickname;

    /**
     * 创建人手机号
     */
    private String creatorPhone;

    /**
     * 创建人手机区号
     */
    private String creatorPhoneAreaCode;

    /**
     * 创建人头像文件ID
     */
    private Long creatorFileIdOfAvatar;

    /**
     * 当前登录用户在该邀约组中的概率值（百分比形式，如：25.5表示25.5%）
     */
    private BigDecimal probability;
}
