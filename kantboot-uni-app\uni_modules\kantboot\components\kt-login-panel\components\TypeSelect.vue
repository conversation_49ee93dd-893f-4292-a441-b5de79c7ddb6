<template>
  <view :class="clazz.typeBox">
    <view v-if="true" class="type-box-btn" :class="{
      'type-box-btn-selected': param.typeCode === 'verificationCode'
    }" @click="selectType('verificationCode')">
      {{ $i18n.zhToGlobal("验证码") }}
      <view v-if="param.typeCode === 'verificationCode'" class="type-box-line"></view>

    </view>
    <view class="type-box-btn" :class="{ 'type-box-btn-selected': param.typeCode === 'password' }"
      @click="selectType('password')">
      {{ $i18n.zhToGlobal("请输入手机号和密码") }}
      <view v-if="param.typeCode === 'password'" class="type-box-line"></view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      clazz: {
        typeBox: this.$kt.style.toggleClass("type-box"),
      },
      param: {
        typeCode: "password",
      }
    };
  },
  created() {
    this.$emit("change", this.param);
  },
  methods: {
    selectType(e) {
      this.param.typeCode = e;
      this.$emit("change", this.param);
    }
  }
}
</script>

<style lang="scss" scoped>
.type-box {
  margin-top: 10rpx;
  position: relative;
  margin-bottom: 10rpx;

  .type-box-btn {
    position: relative;
    display: inline-block;
    font-size: 28rpx;
    text-align: center;
    width: 100%;
    color: #999999;

    // .type-box-line {
    //   position: absolute;
    //   width: 100rpx;
    //   height: 5rpx;
    //   background-color: #000000;
    //   bottom: -10rpx;
    //   border-radius: 10rpx;
    //   left: 50%;
    //   transform: translateX(-50%);
    // }
  }

  .type-box-btn:active {
    opacity: .8;
  }

  .type-box-btn-selected {
    color: #000000;
  }

}

.type-box-mode-color-scheme-light {
  background-color: #fff;
  color: #000;

  .type-box-btn-selected {
    color: #000000;
  }

  .type-box-line {
    background-color: #000000;
  }
}

.type-box-mode-color-scheme-dark {
  color: #fff;

  .type-box-btn {
    color: #fff;

    .type-box-line {
      background-color: #fff;
    }
  }

  .type-box-btn-selected {
    color: #fff;
  }

}
</style>
