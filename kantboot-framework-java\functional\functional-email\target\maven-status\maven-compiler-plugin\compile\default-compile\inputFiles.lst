E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\axigen\AxigenApiClient.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\axigen\service\IAxigenMailService.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\axigen\service\impl\AxigenMailServiceImpl.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\axigen\web\controller\AxigenMailController.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\constants\MessageStatusCodeConstants.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\constants\MessageTypeCodeConstants.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\domain\entity\FunctionalEmail.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\dto\EmailMessageDTO.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\exception\FunctionalEmailException.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\repository\FunctionalEmailRepository.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\service\IFunctionalEmailService.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\service\service\FunctionalEmailServiceImpl.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\setting\FunctionalEmailSetting.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\web\admin\controller\FunctionalEmailControllerOfAdmin.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\functional\functional-email\src\main\java\com\kantboot\functional\email\web\controller\FunctionalEmailController.java
