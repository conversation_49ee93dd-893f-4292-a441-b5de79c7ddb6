<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>starter-parent</artifactId>
    <version>ARISTOTLE-0.1</version>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>starter</artifactId>
  <version>ARISTOTLE-0.1</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>global</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>system-all</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>user-all</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-all</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>official-plugin-all</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>tool-all</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>front-end-manage-all</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>socket-websocket</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>fp-all</artifactId>
    </dependency>
  </dependencies>
</project>
