package com.kantboot.project.make.friends.service;

import com.kantboot.project.make.friends.domain.entity.ProjectMakeFriendsInvitationRelation;
import com.kantboot.util.jpa.param.PageParam;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 邀约关联表服务接口
 * <AUTHOR>
 */
public interface IProjectMakeFriendsInvitationRelationService {

    /**
     * 根据邀约组ID获取成员列表（分页）
     * @param invitationGroupId 邀约组ID
     * @param pageParam 分页参数
     * @return 分页结果
     */
    Page<ProjectMakeFriendsInvitationRelation> getMembersByGroupId(Long invitationGroupId, PageParam<Object> pageParam);

    /**
     * 根据邀约组ID获取成员列表（分页）
     * @param invitationGroupId 邀约组ID
     * @param pageParam 分页参数
     * @param searchText 搜索文本（支持按用户ID、手机号、昵称搜索）
     * @return 分页结果
     */
    Page<ProjectMakeFriendsInvitationRelation> getMembersByGroupId(Long invitationGroupId, PageParam<Object> pageParam, String searchText);

    /**
     * 根据邀约组ID获取所有成员列表
     * @param invitationGroupId 邀约组ID
     * @return 成员列表
     */
    List<ProjectMakeFriendsInvitationRelation> getAllMembersByGroupId(Long invitationGroupId);

    /**
     * 根据用户ID获取参与的邀约组列表
     * @param userAccountId 用户账号ID
     * @return 邀约关联列表
     */
    List<ProjectMakeFriendsInvitationRelation> getGroupsByUserId(Long userAccountId);

    /**
     * 添加成员到邀约组
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 用户账号ID
     * @param probability 概率值
     * @param isInitialCreation 是否初次创建确认
     * @return 邀约关联记录
     */
    ProjectMakeFriendsInvitationRelation addMemberToGroup(Long invitationGroupId, Long userAccountId, BigDecimal probability, Integer isInitialCreation);

    /**
     * 添加成员到邀约组（支持设置参与状态）
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 用户账号ID
     * @param probability 概率值
     * @param isInitialCreation 是否初次创建确认
     * @param participationStatus 参与状态
     * @return 邀约关联记录
     */
    ProjectMakeFriendsInvitationRelation addMemberToGroupWithStatus(Long invitationGroupId, Long userAccountId, BigDecimal probability, Integer isInitialCreation, Integer participationStatus);

    /**
     * 批量添加成员到邀约组
     * @param invitationGroupId 邀约组ID
     * @param userAccountIds 用户账号ID列表
     * @param probability 概率值
     * @param isInitialCreation 是否初次创建确认
     * @return 添加的成员列表
     */
    List<ProjectMakeFriendsInvitationRelation> addMembersToGroup(Long invitationGroupId, List<Long> userAccountIds, BigDecimal probability, Integer isInitialCreation);

    /**
     * 批量添加成员到邀约组（支持每个成员不同概率）
     * @param invitationGroupId 邀约组ID
     * @param memberDataList 成员数据列表，包含用户ID和概率
     * @param isInitialCreation 是否初次创建确认
     * @param participationStatus 参与状态
     * @return 添加的成员列表
     */
    List<ProjectMakeFriendsInvitationRelation> addMembersToGroupWithProbabilities(Long invitationGroupId, List<Map<String, Object>> memberDataList, Integer isInitialCreation, Integer participationStatus);

    /**
     * 批量添加成员到邀约组并自动调整创建人概率
     * @param invitationGroupId 邀约组ID
     * @param memberDataList 成员数据列表，包含用户ID和概率
     * @param creatorUserAccountId 创建人用户ID
     * @param isInitialCreation 是否初次创建确认
     * @param participationStatus 参与状态
     * @return 添加的成员列表
     */
    List<ProjectMakeFriendsInvitationRelation> addMembersToGroupWithCreatorAdjustment(Long invitationGroupId, List<Map<String, Object>> memberDataList, Long creatorUserAccountId, Integer isInitialCreation, Integer participationStatus);

    /**
     * 从邀约组中移除成员
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 用户账号ID
     */
    void removeMemberFromGroup(Long invitationGroupId, Long userAccountId);

    /**
     * 从邀约组中移除成员并将其概率加回给创建人
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 要移除的用户账号ID
     * @param creatorUserAccountId 创建人用户账号ID
     */
    void removeMemberFromGroupWithCreatorAdjustment(Long invitationGroupId, Long userAccountId, Long creatorUserAccountId);

    /**
     * 更新成员参与状态
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 用户账号ID
     * @param participationStatus 参与状态
     * @return 更新后的邀约关联记录
     */
    ProjectMakeFriendsInvitationRelation updateMemberStatus(Long invitationGroupId, Long userAccountId, Integer participationStatus);

    /**
     * 更新成员概率
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 用户账号ID
     * @param probability 新的概率值
     * @return 更新后的邀约关联记录
     */
    ProjectMakeFriendsInvitationRelation updateMemberProbability(Long invitationGroupId, Long userAccountId, BigDecimal probability);

    /**
     * 更新成员概率并自动调整创建人概率以保持总概率为100%
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 要更新的用户账号ID
     * @param newProbability 新的概率值
     * @param creatorUserAccountId 创建人用户账号ID
     * @return 更新后的邀约关联记录
     */
    ProjectMakeFriendsInvitationRelation updateMemberProbabilityWithCreatorAdjustment(
            Long invitationGroupId, Long userAccountId, BigDecimal newProbability, Long creatorUserAccountId);

    /**
     * 批量更新所有成员概率值
     * @param invitationGroupId 邀约组ID
     * @param probability 概率值
     * @return 更新的成员数量
     */
    int updateAllMembersProbability(Long invitationGroupId, BigDecimal probability);

    /**
     * 批量更新成员概率（支持每个成员不同概率）
     * @param invitationGroupId 邀约组ID
     * @param memberProbabilities 成员概率列表，包含用户ID和概率
     * @return 更新的成员数量
     */
    int updateMembersProbabilityBatch(Long invitationGroupId, List<Map<String, Object>> memberProbabilities);

    /**
     * 获取邀约组成员数量
     * @param invitationGroupId 邀约组ID
     * @return 成员数量
     */
    long getMemberCountByGroupId(Long invitationGroupId);

    /**
     * 检查用户是否已在邀约组中
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 用户账号ID
     * @return 是否已在组中
     */
    boolean isMemberInGroup(Long invitationGroupId, Long userAccountId);

    /**
     * 根据ID获取邀约关联记录
     * @param id 主键ID
     * @return 邀约关联记录
     */
    ProjectMakeFriendsInvitationRelation getById(Long id);

    /**
     * 保存邀约关联记录
     * @param entity 邀约关联实体
     * @return 保存后的实体
     */
    ProjectMakeFriendsInvitationRelation save(ProjectMakeFriendsInvitationRelation entity);

    /**
     * 删除邀约关联记录
     * @param id 主键ID
     */
    void deleteById(Long id);

    /**
     * 检查当前用户是否为指定邀约组的协作人
     * @param invitationGroupId 邀约组ID
     * @return 是否为协作人
     */
    boolean isCurrentUserCollaborator(Long invitationGroupId);

    /**
     * 获取用户作为协作人参与的邀约组ID列表（从邀约关系表）
     * @param userAccountId 用户账号ID
     * @return 邀约组ID列表
     */
    List<Long> getInvitationGroupIdsByCollaboratorFromRelation(Long userAccountId);

    /**
     * 根据邀约组ID和用户ID获取邀约关系记录
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 用户账号ID
     * @return 邀约关系记录
     */
    ProjectMakeFriendsInvitationRelation getByInvitationGroupIdAndUserAccountId(Long invitationGroupId, Long userAccountId);
}
