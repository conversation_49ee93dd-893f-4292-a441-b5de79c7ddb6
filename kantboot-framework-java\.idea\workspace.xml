<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6339ec6a-d941-457e-8f16-5acbf6cff3ed" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/domain/dto/ProjectMakeFriendsInvitationGroupWithCreatorDTO.java" beforeDir="false" afterPath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/domain/dto/ProjectMakeFriendsInvitationGroupWithCreatorDTO.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/service/IProjectMakeFriendsInvitationGroupService.java" beforeDir="false" afterPath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/service/IProjectMakeFriendsInvitationGroupService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/service/IProjectMakeFriendsInvitationRelationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/service/IProjectMakeFriendsInvitationRelationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/service/impl/ProjectMakeFriendsInvitationGroupServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/service/impl/ProjectMakeFriendsInvitationGroupServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/service/impl/ProjectMakeFriendsInvitationRelationServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/service/impl/ProjectMakeFriendsInvitationRelationServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/web/controller/ProjectMakeFriendsInvitationGroupController.java" beforeDir="false" afterPath="$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/web/controller/ProjectMakeFriendsInvitationGroupController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../kantboot-uni-app/pages/project-make-friends-pages/member-partner/member-partner-content.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../kantboot-uni-app/pages/project-make-friends-pages/member-partner/member-partner-content.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../kantboot-uni-app/pages/project-make-friends-pages/member-partner/member-partner.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../kantboot-uni-app/pages/project-make-friends-pages/member-partner/member-partner.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../kantboot-uni-app/pages/project-make-friends-pages/member-transfer/member-transfer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../kantboot-uni-app/pages/project-make-friends-pages/member-transfer/member-transfer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../kantboot-uni-app/uni_modules/kantboot/components/kt-login-panel/components/TypeSelect.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../kantboot-uni-app/uni_modules/kantboot/components/kt-login-panel/components/TypeSelect.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../kantboot-vue-admin/src/components/kantboot-framework-vue/libs/request/request.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/../kantboot-vue-admin/src/components/kantboot-framework-vue/libs/request/request.config.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\maven\apache-maven-3.8.8" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.8.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2zud4NWZmNwu0uu6ljIJyHzyH3l" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.parent [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.project-make-friends [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.project-make-friends [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.project-make-friends [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ProjectMakeFriendsApplication.executor&quot;: &quot;Debug&quot;,
    &quot;Spring Boot.ProjectMeetStartApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/gitData/work/bussine/receiving-orders_ll/kant-boot/kantboot-framework-java/project/project-make-friends/src/main/resources&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\project\project-make-friends\src\main\resources" />
      <recent name="E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\project\project-make-friends\src\main\java\com\kantboot\project\make\friends\domain\dto" />
      <recent name="E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\project\project-make-friends\src\main\java\com\kantboot\project\make\friends\web\controller" />
      <recent name="E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\project\project-make-friends\src\main\java\com\kantboot\project\make\friends\service\impl" />
      <recent name="E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\project\project-make-friends\src\main\java\com\kantboot\project\make\friends\service" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.kantboot.project.make.friends.domain.dto" />
      <recent name="com.kantboot.project.make.friends.web.controller" />
      <recent name="com.kantboot.project.make.friends.service.impl" />
      <recent name="com.kantboot.project.make.friends.service" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ProjectMakeFriendsApplication">
    <configuration name="ProjectMakeFriendsApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="project-make-friends" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kantboot.project.make.friends.ProjectMakeFriendsApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProjectMeetStartApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="project-meet" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.kantboot.project.meet.ProjectMeetStartApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6339ec6a-d941-457e-8f16-5acbf6cff3ed" name="Changes" comment="" />
      <created>1752584700870</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752584700870</updated>
      <workItem from="1752584702594" duration="8710000" />
      <workItem from="1752670035290" duration="13843000" />
      <workItem from="1752756047146" duration="11194000" />
      <workItem from="1752769199149" duration="295000" />
      <workItem from="1752842421956" duration="72000" />
      <workItem from="1752842501439" duration="15222000" />
      <workItem from="1752859941230" duration="8140000" />
      <workItem from="1752902540012" duration="1543000" />
      <workItem from="1752926318974" duration="18025000" />
      <workItem from="1752999844638" duration="1689000" />
      <workItem from="1753011168588" duration="1570000" />
      <workItem from="1753013332176" duration="283000" />
      <workItem from="1753019375891" duration="968000" />
      <workItem from="1753026416699" duration="715000" />
      <workItem from="1753421070381" duration="948000" />
      <workItem from="1753507868526" duration="3523000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/project/project-make-friends/src/main/java/com/kantboot/project/make/friends/web/controller/ProjectMakeFriendsCharacteristicController.java</url>
          <line>15</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>