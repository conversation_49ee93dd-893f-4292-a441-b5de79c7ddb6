<template>
  <view>
    <kt-popup :overlayClose="false" ref="ktPopup" @close="close" :zIndex="999999999">
      <view class="box">
        <view class="close-btn" @click="closePop">×</view>
        <view class="title">{{ isEdit ? $i18n.zhToGlobal("修改概率") : $i18n.zhToGlobal("设置概率") }}</view>

        <!-- 用户信息 -->
        <view class="user-info" v-if="userData">
          <view class="info-section">
            <view class="section-title">{{ $i18n.zhToGlobal("选择用户") }}</view>
            <view class="user-card">
              <image class="user-avatar"
                     :src="userData.fileIdOfAvatar ? $kt.file.visit(userData.fileIdOfAvatar) : '/static/default-avatar.png'"
                     mode="aspectFill"></image>
              <view class="user-details">
                <view class="user-nickname">{{ userData.nickname || $i18n.zhToGlobal('用户') }}</view>
                <view class="user-id">ID: {{ userData.id }}</view>
                <view class="user-phone" v-if="userData.phone">
                  {{ userData.phoneAreaCode }} {{ userData.phone }}
                </view>
              </view>
            </view>
          </view>

          <view class="info-section">
            <view class="section-title">{{ $i18n.zhToGlobal("当前可分配概率") }}</view>
            <view class="probability-display">{{ availableProbability }}%</view>
          </view>

          <view class="info-section" v-if="isEdit">
            <view class="section-title">{{ $i18n.zhToGlobal("当前概率") }}</view>
            <view class="current-probability-display">{{ currentProbability }}%</view>
          </view>
        </view>

        <!-- 概率输入 -->
        <view class="probability-section">
          <view class="section-title">{{ $i18n.zhToGlobal("请输入概率值") }}</view>
          <view class="input-container">
            <input
                class="probability-input"
                type="number"
                v-model="inputProbability"
                :placeholder="$i18n.zhToGlobal('请输入0-' + availableProbability + '之间的数值')"
                :max="availableProbability"
                min="0"
            />
            <text class="unit">%</text>
          </view>
          <view class="tips">{{ $i18n.zhToGlobal("概率范围：0-" + availableProbability + "%") }}</view>
        </view>

        <!-- 底部操作按钮 -->
        <view class="footer">
          <view class="buttons">
            <button class="cancel-btn" @click="closePop" :disabled="processing">
              {{ $i18n.zhToGlobal('取消') }}
            </button>
            <button class="confirm-btn" @click="handleConfirm" :disabled="processing">
              {{ processing ? $i18n.zhToGlobal('处理中...') : $i18n.zhToGlobal('确认') }}
            </button>
          </view>
        </view>
      </view>
    </kt-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userData: null,
      availableProbability: 100,
      inputProbability: '',
      processing: false,
      isEdit: false,
      currentProbability: 0
    };
  },
  methods: {
    open(data) {
      this.userData = data.user;
      this.availableProbability = data.availableProbability || 100;
      this.isEdit = data.isEdit || false;
      this.currentProbability = data.currentProbability || 0;
      this.inputProbability = this.isEdit ? this.currentProbability.toString() : '';
      this.processing = false;
      this.$refs.ktPopup.open();
    },

    close() {
      this.userData = null;
      this.availableProbability = 100;
      this.inputProbability = '';
      this.processing = false;
      this.isEdit = false;
      this.currentProbability = 0;
    },

    closePop() {
      this.$refs.ktPopup.close();
    },

    handleConfirm() {
      if (this.processing) return;

      const probability = parseFloat(this.inputProbability);

      if (!probability || probability <= 0) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('请输入有效的概率值'),
          icon: 'none'
        });
        return;
      }

      if (probability > this.availableProbability) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('概率值不能超过' + this.availableProbability + '%'),
          icon: 'none'
        });
        return;
      }

      this.processing = true;

      // 触发确认事件
      this.$emit('confirm', {
        user: this.userData,
        probability: probability,
        isEdit: this.isEdit,
        oldProbability: this.currentProbability
      });

      // 关闭弹窗
      setTimeout(() => {
        this.processing = false;
        this.closePop();
      }, 100);
    }
  }
}
</script>

<style lang="scss" scoped>
.box {
  width: 100vw;
  height: 70vh;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 20rpx 20rpx 0 0;
  position: relative;

  .title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
    padding-top: 20rpx;
  }

  .user-info {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 20rpx;

    .info-section {
      margin-bottom: 30rpx;

      .section-title {
        font-size: 28rpx;
        font-weight: bold;
        margin-bottom: 15rpx;
        color: #333;
      }

      .user-card {
        display: flex;
        align-items: center;
        padding: 20rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;

        .user-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .user-details {
          flex: 1;

          .user-nickname {
            font-size: 30rpx;
            margin-bottom: 6rpx;
            font-weight: bold;
          }

          .user-id,
          .user-phone {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 4rpx;
          }
        }
      }

      .probability-display {
        padding: 20rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;
        font-size: 32rpx;
        color: #000000;
        font-weight: bold;
        text-align: center;
      }

      .current-probability-display {
        padding: 20rpx;
        background-color: #f5f5f5;
        border-radius: 12rpx;
        font-size: 32rpx;
        color: #666666;
        font-weight: bold;
        text-align: center;
        border: 1rpx solid #e0e0e0;
      }
    }
  }

  .probability-section {
    margin-bottom: 30rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 15rpx;
      color: #333;
    }

    .input-container {
      display: flex;
      align-items: center;
      background-color: #f8f8f8;
      border-radius: 12rpx;
      padding: 0 20rpx;

      .probability-input {
        flex: 1;
        height: 80rpx;
        font-size: 32rpx;
        background: transparent;
        border: none;
      }

      .unit {
        font-size: 28rpx;
        color: #666;
        margin-left: 10rpx;
      }
    }

    .tips {
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #999;
      text-align: center;
    }
  }

  .footer {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;

    .buttons {
      display: flex;
      gap: 20rpx;

      button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        font-size: 30rpx;
        border-radius: 40rpx;
        border: none;

        &.cancel-btn {
          background-color: #f5f5f5;
          color: #666;

          &[disabled] {
            background-color: #e0e0e0;
            color: #999;
          }
        }

        &.confirm-btn {
          background-color: #07C160;
          color: #fff;

          &[disabled] {
            background-color: #cccccc;
            color: #fff;
          }
        }
      }
    }
  }
}

.close-btn {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #666;
  z-index: 10;
  cursor: pointer;
  background: #f5f5f5;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
</style>