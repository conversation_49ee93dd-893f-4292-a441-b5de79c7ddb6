<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>api</artifactId>
    <version>ARISTOTLE-0.1</version>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>api-qiniucloud</artifactId>
  <version>ARISTOTLE-0.1</version>
  <packaging>pom</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>api-qiniucloud-setting</module>
    <module>api-qiniucloud-sms</module>
  </modules>
  <properties>
    <qiniucloud.version>7.15.0</qiniucloud.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.qiniu</groupId>
        <artifactId>qiniu-java-sdk</artifactId>
        <version>${qiniucloud.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
