server:
  port: 10012

spring:
  web:
    request-context:
      enabled: true
  mvc:
    async:
      request-timeout: 3600000
  servlet:
    # 上传文件大小限制
    load-on-startup: 1
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  main:
    allow-bean-definition-overriding: true
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      password:
      timeout: 30s
      database: 4
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms
  application:
    name: kantboot
  datasource:
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      idle-timeout: 30000
      max-lifetime: 1800000
      validation-timeout: 5000
#    driver-class-name: org.postgresql.Driver
#    url: ************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************************************************************************************************************
    username: meetov2
    password: woshifangziyuan1
  jpa:
    open-in-view: true
    properties:
      hibernate:
        physical_naming_strategy: com.kantboot.util.jpa.strategy.NoForeignKeyNamingStrategy
        jdbc:
          batch_size: 500
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
    generate-ddl: true
    hibernate:
      ddl-auto: update
#    database: postgresql
    show-sql: false
    database: mysql

kantboot:
  system-setting:
    init:
      enable: true
      # 初始化数据
      settings:
        apiApiWechatApplet:
          appid: wxdcdbe61109a2520b
          secret: 97024d331ac88bbe16725b1755e0610e
        apiBaiduTranslate:
          appid: *****************
          secret: 9LCopmD6f497X_0HzHIf
        apiGoogle:
          projectId: inner-magpie-411919
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          secretKey: AIzaSyAuqabi0_BH31N1Ux59aG3TpDJWZOgEdPg
        apiQiniuCloud:
          accessKey: 4hzTZcLxrRMc2SaQO_Mqbtp_JkMZNVknJPxMSxRA
          secretKey: 0JsUMcDu2g75bOsMqeCpQmEhdd9rARcELST4JH9v
        apiTwilio:
          accountSid: **********************************
          authToken: 3f2ea110cc481f600f0a7c04a0c10a58
          messagingServiceSid: MGade0c26e2dcd7581982dee82e97100de
        apiAlicloudMarket:
          appKey: *********
          appCode: 055272a7524a4a5baec910d494451a19
          appSecret: jnJ7KmH38aCnO3P0Gc3gbjrWI7pRDA5A
        functionalEmail:
          smtpPort: 465
          smtpHost: smtp.qq.com
          smtpUsername: *******
          smtpEmail: *******
          smtpPassword: hzncafrcqwddebdb
