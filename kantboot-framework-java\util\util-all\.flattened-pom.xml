<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>util</artifactId>
    <version>ARISTOTLE-0.1</version>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>util-all</artifactId>
  <version>ARISTOTLE-0.1</version>
  <description>聚合多个实用工具模块，方便在项目中统一引入</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-cache</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-crypto</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-data-change</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-event</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-http</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-jpa</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-rest</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-sc</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-auth</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-file</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-setting</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-i18n</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-qrcode</artifactId>
      <version>${revision}</version>
    </dependency>
  </dependencies>
</project>
