package com.kantboot.project.make.friends.web.controller;

import com.kantboot.project.make.friends.domain.dto.ProjectMakeFriendsInvitationGroupWithCreatorDTO;
import com.kantboot.project.make.friends.domain.entity.ProjectMakeFriendsInvitationGroup;
import com.kantboot.project.make.friends.service.IProjectMakeFriendsInvitationGroupService;
import com.kantboot.util.auth.annotation.AuthInit;
import com.kantboot.util.jpa.param.PageParam;
import com.kantboot.util.rest.consts.CommonSuccessStateConsts;
import com.kantboot.util.rest.result.RestResult;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 邀约协作组控制器
 * <AUTHOR>
 */
@AuthInit(name = "邀约协作组", description = "邀约协作组管理", sourceLanguageCode = "zh_CN")
@RestController
@RequestMapping("/project-make-friends-web/invitationGroup")
public class ProjectMakeFriendsInvitationGroupController {

    @Resource
    private IProjectMakeFriendsInvitationGroupService service;

    /**
     * 获取邀约协作组列表
     */
    @AuthInit(name = "获取邀约协作组列表", description = "获取邀约协作组列表", sourceLanguageCode = "zh_CN")
    @RequestMapping("/getList")
    public RestResult<?> getList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                 @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
                                 @RequestParam(value = "searchText", required = false) String searchText) {
        PageParam<Object> pageParam = new PageParam<>();
        pageParam.setSort("gmtCreate");
        pageParam.setOrderBy("DESC");
        pageParam.setPageNumber(pageNum);
        pageParam.setPageSize(pageSize);

        Page<ProjectMakeFriendsInvitationGroup> pageResult = service.getList(pageParam, searchText);
        return RestResult.success(pageResult.getContent(), CommonSuccessStateConsts.GET_SUCCESS);
    }

    /**
     * 获取邀约协作组列表（包含创建人信息）
     */
    @AuthInit(name = "获取邀约协作组列表（包含创建人信息）", description = "获取邀约协作组列表（包含创建人信息）", sourceLanguageCode = "zh_CN")
    @RequestMapping("/getListWithCreatorInfo")
    public RestResult<?> getListWithCreatorInfo(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
                                                @RequestParam(value = "searchText", required = false) String searchText) {
        PageParam<Object> pageParam = new PageParam<>();
        pageParam.setSort("gmtCreate");
        pageParam.setOrderBy("DESC");
        pageParam.setPageNumber(pageNum);
        pageParam.setPageSize(pageSize);

        Page<ProjectMakeFriendsInvitationGroupWithCreatorDTO> pageResult = service.getListWithCreatorInfo(pageParam, searchText);
        return RestResult.success(pageResult.getContent(), CommonSuccessStateConsts.GET_SUCCESS);
    }

    /**
     * 根据ID获取邀约协作组详情
     */
    @AuthInit(name = "获取邀约协作组详情", description = "根据ID获取邀约协作组详情", sourceLanguageCode = "zh_CN")
    @RequestMapping("/getById")
    public RestResult<?> getById(@RequestParam("id") Long id) {
        ProjectMakeFriendsInvitationGroup result = service.getById(id);
        return RestResult.success(result, CommonSuccessStateConsts.GET_SUCCESS);
    }

    /**
     * 保存邀约协作组
     */
    @AuthInit(name = "保存邀约协作组", description = "保存邀约协作组", sourceLanguageCode = "zh_CN")
    @RequestMapping("/save")
    public RestResult<?> save(@RequestBody ProjectMakeFriendsInvitationGroup entity) {
        ProjectMakeFriendsInvitationGroup result = service.save(entity);
        return RestResult.success(result, CommonSuccessStateConsts.SAVE_SUCCESS);
    }

    /**
     * 删除邀约协作组
     */
    @AuthInit(name = "删除邀约协作组", description = "删除邀约协作组", sourceLanguageCode = "zh_CN")
    @RequestMapping("/deleteById")
    public RestResult<?> deleteById(@RequestParam("id") Long id) {
        service.deleteById(id);
        return RestResult.success(null, CommonSuccessStateConsts.REMOVE_SUCCESS);
    }

    /**
     * 获取我创建的邀约协作组列表
     */
    @AuthInit(name = "获取我创建的邀约协作组列表", description = "获取我创建的邀约协作组列表", sourceLanguageCode = "zh_CN")
    @RequestMapping("/getMyList")
    public RestResult<?> getMyList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                   @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
                                   @RequestParam(value = "searchText", required = false) String searchText) {
        PageParam<Object> pageParam = new PageParam<>();
        pageParam.setSort("gmtCreate");
        pageParam.setOrderBy("DESC");
        pageParam.setPageNumber(pageNum);
        pageParam.setPageSize(pageSize);

        Page<ProjectMakeFriendsInvitationGroup> pageResult = service.getMyList(pageParam, searchText);
        return RestResult.success(pageResult.getContent(), CommonSuccessStateConsts.GET_SUCCESS);
    }

    /**
     * 获取我创建或我是协作人的邀约协作组列表（包含创建人信息）
     */
    @AuthInit(name = "获取我创建或协作的邀约协作组列表", description = "获取我创建或我是协作人的邀约协作组列表（包含创建人信息）", sourceLanguageCode = "zh_CN")
    @RequestMapping("/getMyCreatedOrCollaboratedListWithCreatorInfo")
    public RestResult<?> getMyCreatedOrCollaboratedListWithCreatorInfo(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                       @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
                                                                       @RequestParam(value = "searchText", required = false) String searchText,
                                                                       @RequestParam(value = "sortType", defaultValue = "time") String sortType) {
        PageParam<Object> pageParam = new PageParam<>();

        // 根据排序类型设置排序字段
        if ("probability".equals(sortType)) {
            pageParam.setSort("probability");
            pageParam.setOrderBy("ASC"); // 概率从小到大
        } else {
            pageParam.setSort("gmtCreate");
            pageParam.setOrderBy("DESC"); // 时间从近到远
        }

        pageParam.setPageNumber(pageNum);
        pageParam.setPageSize(pageSize);

        Page<ProjectMakeFriendsInvitationGroupWithCreatorDTO> pageResult = service.getMyCreatedOrCollaboratedListWithCreatorInfo(pageParam, searchText, sortType);
        return RestResult.success(pageResult.getContent(), CommonSuccessStateConsts.GET_SUCCESS);
    }

    /**
     * 创建邀约协作组并添加成员
     */
    @AuthInit(name = "创建邀约协作组并添加成员", description = "创建邀约协作组并添加成员", sourceLanguageCode = "zh_CN")
    @RequestMapping("/createGroupWithMembers")
    public RestResult<?> createGroupWithMembers(@RequestBody Map<String, Object> params) {
        String groupName = params.get("groupName").toString();
        String description = params.get("description") != null ? params.get("description").toString() : "";
        @SuppressWarnings("unchecked")
        List<Long> userAccountIds = (List<Long>) params.get("userAccountIds");
        BigDecimal probability = params.get("probability") != null ?
            new BigDecimal(params.get("probability").toString()) : BigDecimal.ZERO;

        ProjectMakeFriendsInvitationGroup result = service.createGroupWithMembers(groupName, description, userAccountIds, probability);
        return RestResult.success(result, CommonSuccessStateConsts.SAVE_SUCCESS);
    }

    /**
     * 新增共同邀约人（快速创建）
     */
    @AuthInit(name = "新增共同邀约人", description = "新增共同邀约人（快速创建）", sourceLanguageCode = "zh_CN")
    @RequestMapping("/createNewInvitationGroup")
    public RestResult<?> createNewInvitationGroup(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> userAccountIds = (List<Long>) params.get("userAccountIds");

        ProjectMakeFriendsInvitationGroup result = service.createNewInvitationGroup(userAccountIds);
        return RestResult.success(result, CommonSuccessStateConsts.SAVE_SUCCESS);
    }

    /**
     * 新增共同邀约人（支持概率设置）
     */
    @AuthInit(name = "新增共同邀约人（支持概率设置）", description = "新增共同邀约人（支持概率设置）", sourceLanguageCode = "zh_CN")
    @RequestMapping("/createNewInvitationGroupWithProbability")
    public RestResult<?> createNewInvitationGroupWithProbability(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> usersData = (List<Map<String, Object>>) params.get("users");
        Integer defaultParticipationStatus = params.get("defaultParticipationStatus") != null ?
            Integer.parseInt(params.get("defaultParticipationStatus").toString()) : 1;

        // 转换用户数据
        List<IProjectMakeFriendsInvitationGroupService.UserProbabilityInfo> usersWithProbability =
            usersData.stream().map(userData -> {
                Long userAccountId = Long.parseLong(userData.get("userAccountId").toString());
                BigDecimal probability = new BigDecimal(userData.get("probability").toString());
                return new IProjectMakeFriendsInvitationGroupService.UserProbabilityInfo(userAccountId, probability);
            }).toList();

        ProjectMakeFriendsInvitationGroup result = service.createNewInvitationGroupWithProbability(usersWithProbability, defaultParticipationStatus);
        return RestResult.success(result, CommonSuccessStateConsts.SAVE_SUCCESS);
    }

    /**
     * 新增共同邀约人（带项目名称和概率）
     */
    @AuthInit(name = "新增共同邀约人（带项目名称和概率）", description = "新增共同邀约人（带项目名称和概率）", sourceLanguageCode = "zh_CN")
    @RequestMapping("/createNewInvitationGroupWithProbabilityAndName")
    public RestResult<?> createNewInvitationGroupWithProbabilityAndName(@RequestBody Map<String, Object> params) {
        String groupName = params.get("groupName").toString();
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> usersData = (List<Map<String, Object>>) params.get("users");
        Integer defaultParticipationStatus = params.get("defaultParticipationStatus") != null ?
            Integer.parseInt(params.get("defaultParticipationStatus").toString()) : 1;

        // 转换用户数据
        List<IProjectMakeFriendsInvitationGroupService.UserProbabilityInfo> usersWithProbability =
            usersData.stream().map(userData -> {
                Long userAccountId = Long.parseLong(userData.get("userAccountId").toString());
                BigDecimal probability = new BigDecimal(userData.get("probability").toString());
                return new IProjectMakeFriendsInvitationGroupService.UserProbabilityInfo(userAccountId, probability);
            }).toList();

        ProjectMakeFriendsInvitationGroup result = service.createNewInvitationGroupWithProbabilityAndName(groupName, usersWithProbability, defaultParticipationStatus);
        return RestResult.success(result, CommonSuccessStateConsts.SAVE_SUCCESS);
    }

    /**
     * 根据组概率随机获取用户ID
     */
    @AuthInit(name = "根据组概率获取用户ID", description = "根据邀约组ID和概率随机选择一个用户ID", sourceLanguageCode = "zh_CN")
    @RequestMapping("/getUserIdByGroupProbability")
    public RestResult<?> getUserIdByGroupProbability(@RequestParam("invitationGroupId") Long invitationGroupId) {
        Long userId = service.getUserIdByGroupProbability(invitationGroupId);
        return RestResult.success(userId, CommonSuccessStateConsts.GET_SUCCESS);
    }
}
