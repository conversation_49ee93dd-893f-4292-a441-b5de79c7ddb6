package com.kantboot.project.make.friends.service.impl;

import com.kantboot.project.make.friends.dao.repository.ProjectMakeFriendsInvitationGroupRepository;
import com.kantboot.project.make.friends.dao.repository.ProjectMakeFriendsInvitationRelationRepository;
import com.kantboot.project.make.friends.domain.entity.ProjectMakeFriendsInvitationGroup;
import com.kantboot.project.make.friends.domain.entity.ProjectMakeFriendsInvitationRelation;
import com.kantboot.project.make.friends.service.IProjectMakeFriendsInvitationRelationService;
import com.kantboot.user.account.service.IUserAccountService;
import com.kantboot.util.jpa.param.PageParam;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 邀约关联表服务实现类
 * <AUTHOR>
 */
@Service
public class ProjectMakeFriendsInvitationRelationServiceImpl implements IProjectMakeFriendsInvitationRelationService {

    @Resource
    private ProjectMakeFriendsInvitationRelationRepository repository;

    @Resource
    private ProjectMakeFriendsInvitationGroupRepository groupRepository;

    @Resource
    private IUserAccountService userAccountService;

    @Override
    public Page<ProjectMakeFriendsInvitationRelation> getMembersByGroupId(Long invitationGroupId, PageParam<Object> pageParam) {
        return getMembersByGroupId(invitationGroupId, pageParam, null);
    }

    @Override
    public Page<ProjectMakeFriendsInvitationRelation> getMembersByGroupId(Long invitationGroupId, PageParam<Object> pageParam, String searchText) {
        PageRequest pageRequest = PageRequest.of(pageParam.getPageNumber() - 1, pageParam.getPageSize());

        // 获取创建人用户ID
        ProjectMakeFriendsInvitationGroup group = groupRepository.findById(invitationGroupId).orElse(null);
        Long creatorUserAccountId = group != null ? group.getCreatorUserAccountId() : null;

        if (creatorUserAccountId == null) {
            // 如果无法获取创建人ID，使用原方法
            if (searchText != null && !searchText.trim().isEmpty()) {
                return repository.findByInvitationGroupIdWithSearch(invitationGroupId, searchText.trim(), pageRequest);
            } else {
                return repository.findByInvitationGroupId(invitationGroupId, pageRequest);
            }
        }

        if (searchText != null && !searchText.trim().isEmpty()) {
            // 使用带排序的搜索功能
            return repository.findByInvitationGroupIdWithSearchOrderByCreatorAndCollaborator(
                    invitationGroupId, creatorUserAccountId, searchText.trim(), pageRequest);
        } else {
            // 使用带排序的查询功能
            return repository.findByInvitationGroupIdOrderByCreatorAndCollaborator(
                    invitationGroupId, creatorUserAccountId, pageRequest);
        }
    }

    @Override
    public List<ProjectMakeFriendsInvitationRelation> getAllMembersByGroupId(Long invitationGroupId) {
        return repository.findByInvitationGroupId(invitationGroupId);
    }

    @Override
    public List<ProjectMakeFriendsInvitationRelation> getGroupsByUserId(Long userAccountId) {
        return repository.findByUserAccountId(userAccountId);
    }

    @Override
    @Transactional
    public ProjectMakeFriendsInvitationRelation addMemberToGroup(Long invitationGroupId, Long userAccountId, BigDecimal probability, Integer isInitialCreation) {
        return addMemberToGroupWithStatus(invitationGroupId, userAccountId, probability, isInitialCreation, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectMakeFriendsInvitationRelation addMemberToGroupWithStatus(Long invitationGroupId, Long userAccountId, BigDecimal probability, Integer isInitialCreation, Integer participationStatus) {
        // 检查是否已存在
        ProjectMakeFriendsInvitationRelation existing = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
        if (existing != null) {
            throw new RuntimeException("用户已在该邀约组中");
        }

        // 获取邀约组信息，判断是否是创建人
        ProjectMakeFriendsInvitationGroup group = groupRepository.findById(invitationGroupId).orElse(null);
        boolean isCreator = group != null && group.getCreatorUserAccountId().equals(userAccountId);

        ProjectMakeFriendsInvitationRelation relation = new ProjectMakeFriendsInvitationRelation();
        relation.setInvitationGroupId(invitationGroupId);
        relation.setUserAccountId(userAccountId);
        relation.setProbability(probability);
        relation.setIsInitialCreation(isInitialCreation);
        relation.setParticipationStatus(participationStatus != null ? participationStatus : 0); // 支持自定义参与状态
        relation.setIsCollaborator(isCreator ? 1 : 0); // 创建人默认标记为协作人
        relation.setInvitationTime(new Date());

        return repository.save(relation);
    }

    @Override
    @Transactional
    public List<ProjectMakeFriendsInvitationRelation> addMembersToGroup(Long invitationGroupId, List<Long> userAccountIds, BigDecimal probability, Integer isInitialCreation) {
        List<ProjectMakeFriendsInvitationRelation> results = new ArrayList<>();

        for (Long userAccountId : userAccountIds) {
            try {
                ProjectMakeFriendsInvitationRelation relation = addMemberToGroup(invitationGroupId, userAccountId, probability, isInitialCreation);
                results.add(relation);
            } catch (RuntimeException e) {
                // 如果用户已存在，跳过
                continue;
            }
        }

        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProjectMakeFriendsInvitationRelation> addMembersToGroupWithProbabilities(Long invitationGroupId, List<Map<String, Object>> memberDataList, Integer isInitialCreation, Integer participationStatus) {
        List<ProjectMakeFriendsInvitationRelation> results = new ArrayList<>();

        for (Map<String, Object> memberData : memberDataList) {
            try {
                Long userAccountId = Long.parseLong(memberData.get("userAccountId").toString());
                BigDecimal probability = new BigDecimal(memberData.get("probability").toString());

                // 检查是否已存在
                ProjectMakeFriendsInvitationRelation existing = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
                if (existing != null) {
                    // 如果用户已存在，跳过
                    continue;
                }

                ProjectMakeFriendsInvitationRelation relation = new ProjectMakeFriendsInvitationRelation();
                relation.setInvitationGroupId(invitationGroupId);
                relation.setUserAccountId(userAccountId);
                relation.setProbability(probability);
                relation.setIsInitialCreation(isInitialCreation);
                relation.setParticipationStatus(participationStatus != null ? participationStatus : 0);
                relation.setInvitationTime(new Date());

                relation = repository.save(relation);
                results.add(relation);
            } catch (Exception e) {
                // 记录错误但不中断整个事务
                System.err.println("添加成员失败: " + e.getMessage());
                continue;
            }
        }

        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProjectMakeFriendsInvitationRelation> addMembersToGroupWithCreatorAdjustment(Long invitationGroupId, List<Map<String, Object>> memberDataList, Long creatorUserAccountId, Integer isInitialCreation, Integer participationStatus) {
        List<ProjectMakeFriendsInvitationRelation> results = new ArrayList<>();
        BigDecimal totalProbabilityToDeduct = BigDecimal.ZERO;

        // 先添加所有成员并计算总概率
        for (Map<String, Object> memberData : memberDataList) {
            try {
                Long userAccountId = Long.parseLong(memberData.get("userAccountId").toString());
                BigDecimal probability = new BigDecimal(memberData.get("probability").toString());

                // 检查是否已存在
                ProjectMakeFriendsInvitationRelation existing = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
                if (existing != null) {
                    // 如果用户已存在，跳过
                    continue;
                }

                ProjectMakeFriendsInvitationRelation relation = new ProjectMakeFriendsInvitationRelation();
                relation.setInvitationGroupId(invitationGroupId);
                relation.setUserAccountId(userAccountId);
                relation.setProbability(probability);
                relation.setIsInitialCreation(isInitialCreation);
                relation.setParticipationStatus(participationStatus != null ? participationStatus : 0);
                relation.setInvitationTime(new Date());

                relation = repository.save(relation);
                results.add(relation);

                // 累计需要从创建人扣减的概率
                totalProbabilityToDeduct = totalProbabilityToDeduct.add(probability);
            } catch (Exception e) {
                // 记录错误但不中断整个事务
                System.err.println("添加成员失败: " + e.getMessage());
                continue;
            }
        }

        // 如果有成功添加的成员，则调整创建人的概率
        if (totalProbabilityToDeduct.compareTo(BigDecimal.ZERO) > 0) {
            ProjectMakeFriendsInvitationRelation creatorRelation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, creatorUserAccountId);
            if (creatorRelation != null) {
                BigDecimal newCreatorProbability = creatorRelation.getProbability().subtract(totalProbabilityToDeduct);
                // 确保概率不为负数
                if (newCreatorProbability.compareTo(BigDecimal.ZERO) < 0) {
                    newCreatorProbability = BigDecimal.ZERO;
                }
                creatorRelation.setProbability(newCreatorProbability);
                repository.save(creatorRelation);
            }
        }

        return results;
    }

    @Override
    @Transactional
    public void removeMemberFromGroup(Long invitationGroupId, Long userAccountId) {
        ProjectMakeFriendsInvitationRelation relation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
        if (relation != null) {
            repository.delete(relation);
        }
    }

    @Override
    @Transactional
    public void removeMemberFromGroupWithCreatorAdjustment(Long invitationGroupId, Long userAccountId, Long creatorUserAccountId) {
        // 获取要删除的成员记录
        ProjectMakeFriendsInvitationRelation memberRelation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
        if (memberRelation == null) {
            throw new RuntimeException("未找到该成员记录");
        }

        // 获取创建人记录
        ProjectMakeFriendsInvitationRelation creatorRelation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, creatorUserAccountId);
        if (creatorRelation == null) {
            throw new RuntimeException("未找到创建人记录");
        }

        // 获取被删除成员的概率
        BigDecimal memberProbability = memberRelation.getProbability();

        // 删除成员记录
        repository.delete(memberRelation);

        // 将删除成员的概率加回给创建人
        BigDecimal newCreatorProbability = creatorRelation.getProbability().add(memberProbability);
        creatorRelation.setProbability(newCreatorProbability);
        repository.save(creatorRelation);
    }

    @Override
    @Transactional
    public ProjectMakeFriendsInvitationRelation updateMemberStatus(Long invitationGroupId, Long userAccountId, Integer participationStatus) {
        ProjectMakeFriendsInvitationRelation relation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
        if (relation == null) {
            throw new RuntimeException("未找到该成员记录");
        }

        relation.setParticipationStatus(participationStatus);
        relation.setResponseTime(new Date());

        return repository.save(relation);
    }

    @Override
    @Transactional
    public ProjectMakeFriendsInvitationRelation updateMemberProbability(Long invitationGroupId, Long userAccountId, BigDecimal probability) {
        ProjectMakeFriendsInvitationRelation relation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
        if (relation == null) {
            throw new RuntimeException("未找到该成员记录");
        }

        relation.setProbability(probability);

        return repository.save(relation);
    }

    @Override
    @Transactional
    public int updateAllMembersProbability(Long invitationGroupId, BigDecimal probability) {
        List<ProjectMakeFriendsInvitationRelation> members = repository.findByInvitationGroupId(invitationGroupId);

        for (ProjectMakeFriendsInvitationRelation member : members) {
            member.setProbability(probability);
        }

        repository.saveAll(members);
        return members.size();
    }

    @Override
    @Transactional
    public int updateMembersProbabilityBatch(Long invitationGroupId, List<Map<String, Object>> memberProbabilities) {
        int updatedCount = 0;

        for (Map<String, Object> memberData : memberProbabilities) {
            try {
                Long userAccountId = Long.parseLong(memberData.get("userAccountId").toString());
                BigDecimal probability = new BigDecimal(memberData.get("probability").toString());

                ProjectMakeFriendsInvitationRelation relation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
                if (relation != null) {
                    relation.setProbability(probability);
                    repository.save(relation);
                    updatedCount++;
                }
            } catch (Exception e) {
                // 记录错误但不中断整个事务
                System.err.println("更新成员概率失败: " + e.getMessage());
                continue;
            }
        }

        return updatedCount;
    }

    /**
     * 更新成员概率并自动调整创建人概率以保持总概率为100%
     * @param invitationGroupId 邀约组ID
     * @param userAccountId 要更新的用户账号ID
     * @param newProbability 新的概率值
     * @param creatorUserAccountId 创建人用户账号ID
     * @return 更新后的邀约关联记录
     */
    @Transactional
    public ProjectMakeFriendsInvitationRelation updateMemberProbabilityWithCreatorAdjustment(
            Long invitationGroupId, Long userAccountId, BigDecimal newProbability, Long creatorUserAccountId) {

        // 获取要更新的成员记录
        ProjectMakeFriendsInvitationRelation memberRelation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
        if (memberRelation == null) {
            throw new RuntimeException("未找到该成员记录");
        }

        // 获取创建人记录
        ProjectMakeFriendsInvitationRelation creatorRelation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, creatorUserAccountId);
        if (creatorRelation == null) {
            throw new RuntimeException("未找到创建人记录");
        }

        // 计算概率差值
        BigDecimal oldProbability = memberRelation.getProbability();
        BigDecimal probabilityDiff = oldProbability.subtract(newProbability);

        // 更新成员概率
        memberRelation.setProbability(newProbability);

        // 调整创建人概率
        BigDecimal newCreatorProbability = creatorRelation.getProbability().add(probabilityDiff);
        creatorRelation.setProbability(newCreatorProbability);

        // 保存更新
        repository.save(memberRelation);
        repository.save(creatorRelation);

        return memberRelation;
    }

    @Override
    public long getMemberCountByGroupId(Long invitationGroupId) {
        return repository.countByInvitationGroupId(invitationGroupId);
    }

    @Override
    public boolean isMemberInGroup(Long invitationGroupId, Long userAccountId) {
        ProjectMakeFriendsInvitationRelation relation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
        return relation != null;
    }

    @Override
    public ProjectMakeFriendsInvitationRelation getById(Long id) {
        return repository.findById(id).orElse(null);
    }

    @Override
    public ProjectMakeFriendsInvitationRelation save(ProjectMakeFriendsInvitationRelation entity) {
        return repository.save(entity);
    }

    @Override
    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    @Override
    public boolean isCurrentUserCollaborator(Long invitationGroupId) {
        // 获取当前登录用户ID
        Long currentUserId = userAccountService.getSelfId();

        // 查询用户在该邀约组中的记录
        ProjectMakeFriendsInvitationRelation relation = repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, currentUserId);

        // 如果记录存在且标记为协作人，则返回true
        return relation != null && relation.getIsCollaborator() != null && relation.getIsCollaborator() == 1;
    }

    @Override
    public List<Long> getInvitationGroupIdsByCollaboratorFromRelation(Long userAccountId) {
//        return repository.findInvitationGroupIdsByUserAccountIdAndIsCollaborator(userAccountId, 1);
        return repository.findInvitationGroupIdsByUserAccountId(userAccountId);
    }

    @Override
    public ProjectMakeFriendsInvitationRelation getByInvitationGroupIdAndUserAccountId(Long invitationGroupId, Long userAccountId) {
        return repository.findByInvitationGroupIdAndUserAccountId(invitationGroupId, userAccountId);
    }
}
