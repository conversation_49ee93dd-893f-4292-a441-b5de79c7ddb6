<template>
  <view>
    <!-- 添加渐变背景 -->
    <view class="gradient-background"></view>

    <view class="header" id="headerInMemberPartnerContent">
      <kt-nav-bar :title="$i18n.zhToGlobal('共同邀约')" :showBack="true"></kt-nav-bar>
    </view>

    <!-- 内容区域 -->
    <view class="content-box">
      <!-- 二维码区域 -->
      <view class="qrcode-section">
        <view class="qrcode-container">
          <kt-qrcode v-if="qrcodeContent" :content="qrcodeContent" size="260rpx" class="centered-qrcode">
          </kt-qrcode>
          <view v-else class="qrcode-loading">
            <text>{{ $i18n.zhToGlobal('二维码生成中...') }}</text>
          </view>
        </view>
        <view class="qrcode-tip">
          {{ $i18n.zhToGlobal('点开本交友小程序上的个人中心页面中的扫一扫按钮，扫描二维码加入项目') }}
        </view>
      </view>

      <!-- 搜索框 -->
      <view class="box-search">
        <view class="search-box">
          <u-input :placeholder="$i18n.zhToGlobal('请输入ID、手机号或姓名搜索')" @confirm="searchMembers" v-model="searchKeyword"
            @input="onSearchInput">
            <template slot="suffix">
              <u-button @tap="searchMembers" class="search-btn" :disabled="!searchKeyword">搜索</u-button>
            </template>
          </u-input>
        </view>
      </view>

      <!-- 参与总人数 - 移到搜索框下面 -->
      <view class="member-count-section">
        <text class="member-count-text">{{ $i18n.zhToGlobal('参与总人数') }}{{ memberCount }}{{ $i18n.zhToGlobal('人')
          }}</text>
        <view class="refresh-btn" @click="refreshData">
          <text>{{ $i18n.zhToGlobal('刷新') }}</text>
        </view>
      </view>

      <!-- 成员列表 -->
      <view class="box">
        <view v-if="memberList.length === 0 && !isLoading" class="empty-tip">
          {{ $i18n.zhToGlobal('暂无参与人') }}
        </view>

        <!-- 成员卡片列表 -->
        <view class="box-item" v-for="member in memberList" :key="member.id">
          <view class="box-item-card">
            <!-- 上半部分：名称+ID 和 概率值 -->
            <view class="card-top-section">
              <view class="name-id-section">
                <view class="member-avatar">
                  <kt-avatar v-if="member.userInfo && member.userInfo.fileIdOfAvatar" size="60rpx"
                    :src="$kt.file.visit(member.userInfo.fileIdOfAvatar)">
                  </kt-avatar>
                  <kt-avatar v-else size="60rpx" :src="$kt.file.byPath('/image/logo.png')"></kt-avatar>
                </view>
                <view class="member-info">
                  <view class="member-name">{{ member.userInfo ? (member.userInfo.nickname ||
                    `用户${member.userAccountId}`) :
                    `用户${member.userAccountId}` }}</view>
                  <view class="member-id">ID: {{ member.userAccountId }}</view>
                </view>
              </view>
              <view class="probability-section">
                <view class="probability-label">概率</view>
                <view class="probability-value">{{ (member.probability * 100).toFixed(0) }}%</view>
              </view>
            </view>

            <!-- 下半部分：联系按钮 和 修改删除按钮 -->
            <view class="card-bottom-section">
              <view class="contact-section">
                <view class="contact-btn" @click.stop="contactUser(member)">
                  联系他（她）
                </view>
              </view>
              <view class="action-section" @click.stop v-if="isCurrentUserCollaborator && member.userAccountId !== creatorUserId && member.isCollaborator !== 1">
                <view class="action-btn-text edit-btn" @click="editMemberProbability(member)">
                  修改
                </view>
                <view class="action-btn-text delete-btn" @click="doDeleteMember(member)">
                  删除
                </view>
              </view>
              <view class="tag-section" v-else>
                <view class="creator-tag" v-if="member.userAccountId === creatorUserId">
                  {{ $i18n.zhToGlobal('创建人') }}
                </view>
                <view class="collaborator-tag" v-else-if="member.isCollaborator === 1">
                  {{ $i18n.zhToGlobal('协作人') }}
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <u-loading-icon v-if="isLoading" mode="circle" size="50rpx"></u-loading-icon>
        <view v-if="isBottom && memberList.length > 0" class="load-more-tip">
          {{ $i18n.zhToGlobal('没有更多了') }}
        </view>
      </view>

      <!-- 底部按钮区域 - 修改为一列布局 -->
      <view class="bottom-actions">
        <!-- 新增共同邀约人按钮 -->
        <view class="action-btn wechat-style-btn" @click="addNewInvitationGroup" v-if="isCurrentUserCollaborator">
          <text>{{ $i18n.zhToGlobal('新增共同邀约人') }}</text>
        </view>

        <!-- 添加协作人按钮 -->
        <view class="action-btn wechat-style-btn" @click="addCollaborator" v-if="isCurrentUserCollaborator">
          <text>{{ $i18n.zhToGlobal('添加协作人') }}</text>
        </view>

        <!-- 一键修改所有人比例按钮 -->
        <view class="action-btn wechat-style-btn" @click="showProbabilityModal" v-if="isCurrentUserCollaborator">
          <text>{{ $i18n.zhToGlobal('一键修改所有人比例') }}</text>
        </view>

        <!-- 生成二维码按钮 -->
        <view class="action-btn wechat-style-btn" @click="generateMiniProgramCode">
          <text>{{ $i18n.zhToGlobal('生成二维码') }}</text>
        </view>
      </view>
    </view>

    <!-- 均分概率弹窗 -->
    <project-make-friends-equal-probability-popup ref="equalProbabilityPopup" @confirm="onEqualProbabilityConfirm">
    </project-make-friends-equal-probability-popup>

    <!-- 单个成员概率修改弹窗 -->
    <project-make-friends-partner-popup ref="memberProbabilityPopup" @confirm="onMemberProbabilityConfirm">
    </project-make-friends-partner-popup>
  </view>
</template>

<script>
import ProjectMakeFriendsPartnerPopup from '@/uni_modules/project-make-friends/components/project-make-friends-partner-popup/project-make-friends-partner-popup.vue'
import ProjectMakeFriendsEqualProbabilityPopup from '@/uni_modules/project-make-friends/components/project-make-friends-equal-probability-popup/project-make-friends-equal-probability-popup.vue'
import KtAvatar from "../../../uni_modules/kantboot/components/kt-avatar/kt-avatar.vue";

export default {
  components: {
    ProjectMakeFriendsPartnerPopup,
    ProjectMakeFriendsEqualProbabilityPopup,
    KtAvatar
  },
  data() {
    return {
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      },
      invitationGroupId: null, // 邀约组ID
      memberList: [], // 成员列表
      memberCount: 0, // 成员总数
      searchKeyword: '', // 搜索关键词
      isLoading: false, // 是否加载中
      isBottom: false, // 是否到底部
      pageNum: 1,
      pageSize: 20,
      qrcodeContent: '', // 二维码内容
      currentEditMember: null, // 当前编辑的成员
      creatorUserId: null, // 创建人用户ID
      isCurrentUserCollaborator: false, // 当前用户是否为协作人
    }
  },
  onLoad(options) {
    // 获取传入的邀约组ID
    if (options.invitationGroupId) {
      this.invitationGroupId = options.invitationGroupId;
      this.initData();
    } else {
      uni.showToast({
        title: this.$i18n.zhToGlobal('参数错误'),
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  onReachBottom() {
    this.loadMore();
  },
  methods: {
    // 初始化数据
    initData() {
      this.generateQRCode();
      this.getMemberCount();
      this.getMemberList();
      this.getGroupInfo();
      this.checkCurrentUserCollaborator();
    },

    // 生成二维码内容
    generateQRCode() {
      // 确保invitationGroupId存在
      if (!this.invitationGroupId) {
        console.error('invitationGroupId为空，无法生成二维码');
        return;
      }

      // 生成二维码内容
      const qrData = {
        operateCode: 'member-partner',
        invitationGroupId: this.invitationGroupId
      };
      this.qrcodeContent = JSON.stringify(qrData);
      console.log('生成的二维码内容:', this.qrcodeContent);
    },

    // 获取成员总数
    getMemberCount() {
      this.$request.post('/project-make-friends-web/invitationRelation/getMemberCount', {
        data: {
          invitationGroupId: this.invitationGroupId
        }
      }).then(res => {
        if (res.state === 2000) {
          this.memberCount = res.data || 0;
        }
      }).catch(err => {
        console.error('获取成员数量失败:', err);
      });
    },

    // 获取成员列表
    getMemberList(isRefresh = false) {
      if (this.isLoading) return;

      this.isLoading = true;

      if (isRefresh) {
        this.pageNum = 1;
        this.isBottom = false;
        this.memberList = [];
      }

      this.$request.post('/project-make-friends-web/invitationRelation/getMembersByGroupId', {
        data: {
          invitationGroupId: this.invitationGroupId,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          searchText: this.searchKeyword.trim() || null
        }
      }).then(res => {
        if (res.state === 2000) {
          const newData = res.data || [];

          if (isRefresh) {
            this.memberList = newData;
          } else {
            this.memberList = [...this.memberList, ...newData];
          }

          if (newData.length < this.pageSize) {
            this.isBottom = true;
          }

          // 获取用户信息
          this.loadUserInfo();
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('获取成员列表失败'),
            icon: 'none'
          });
        }
        this.isLoading = false;
      }).catch(err => {
        console.error('获取成员列表失败:', err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
        this.isLoading = false;
      });
    },

    // 加载用户信息
    async loadUserInfo() {
      // 获取所有需要查询用户信息的用户ID
      const userIds = this.memberList
        .filter(member => !member.userInfo)
        .map(member => member.userAccountId);

      if (userIds.length === 0) return;

      try {
        // 批量获取用户信息
        const promises = userIds.map(userId =>
          this.$kt.userAccount.getById(userId).catch(err => {
            console.error(`获取用户${userId}信息失败:`, err);
            return {
              id: userId,
              nickname: `用户${userId}`,
              fileIdOfAvatar: null,
              phone: null
            };
          })
        );

        const userInfoList = await Promise.all(promises);

        // 将用户信息关联到成员列表
        this.memberList.forEach(member => {
          if (!member.userInfo) {
            const userInfo = userInfoList.find(user => user.id === member.userAccountId);
            if (userInfo) {
              member.userInfo = userInfo;
            } else {
              // 如果没有找到用户信息，使用默认信息
              member.userInfo = {
                id: member.userAccountId,
                nickname: `用户${member.userAccountId}`,
                fileIdOfAvatar: null,
                phone: null
              };
            }
          }
        });

        // 强制更新视图
        this.$forceUpdate();
      } catch (error) {
        console.error('批量获取用户信息失败:', error);
        // 如果批量获取失败，使用默认信息
        this.memberList.forEach(member => {
          if (!member.userInfo) {
            member.userInfo = {
              id: member.userAccountId,
              nickname: `用户${member.userAccountId}`,
              fileIdOfAvatar: null,
              phone: null
            };
          }
        });
      }
    },

    // 搜索输入
    onSearchInput(e) {
      this.searchKeyword = e.detail.value;
      // 可以添加防抖搜索逻辑
      this.debounceSearch();
    },

    // 搜索成员
    searchMembers() {
      this.getMemberList(true);
    },

    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.searchMembers();
      }, 500);
    },

    // 加载更多
    loadMore() {
      if (this.isLoading || this.isBottom) return;

      this.pageNum++;
      this.getMemberList();
    },

    // 刷新数据
    refreshData() {
      this.getMemberCount();
      this.getMemberList(true);
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待确认',
        1: '已接受',
        2: '已拒绝',
        3: '已退出'
      };
      return this.$i18n.zhToGlobal(statusMap[status] || '未知状态');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: 'status-pending',
        1: 'status-accepted',
        2: 'status-rejected',
        3: 'status-exited'
      };
      return classMap[status] || 'status-unknown';
    },

    // 新增共同邀约人
    addNewInvitationGroup() {
      // 跳转到用户选择页面
      uni.navigateTo({
        url: `/pages/project-make-friends-pages/member-partner/user-select?mode=addToGroup&groupId=${this.invitationGroupId}`,
        events: {
          // 监听添加成功事件
          addSuccess: (data) => {
            console.log('添加成功回调:', data);
            // 刷新当前页面数据
            this.refreshData();
          }
        },
        success: (res) => {
          console.log('页面跳转成功');
        },
        fail: (err) => {
          console.error('页面跳转失败:', err);
          uni.showToast({
            title: this.$i18n.zhToGlobal('页面跳转失败'),
            icon: 'none'
          });
        }
      });
    },

    // 添加协作人
    addCollaborator() {
      // 跳转到协作人选择页面
      uni.navigateTo({
        url: `/pages/project-make-friends-pages/member-partner/collaborator-select?invitationGroupId=${this.invitationGroupId}`,
        events: {
          // 监听添加成功事件
          collaboratorAddSuccess: (data) => {
            console.log('协作人添加成功回调:', data);
            // 刷新当前页面数据
            this.refreshData();
            uni.showToast({
              title: this.$i18n.zhToGlobal('协作人操作成功'),
              icon: 'success'
            });
          }
        },
        success: (res) => {
          console.log('协作人选择页面跳转成功');
        },
        fail: (err) => {
          console.error('协作人选择页面跳转失败:', err);
          uni.showToast({
            title: this.$i18n.zhToGlobal('页面跳转失败'),
            icon: 'none'
          });
        }
      });
    },

    // 显示均分概率弹窗
    showProbabilityModal() {
      if (this.memberList.length === 0) {
        uni.showToast({
          title: this.$i18n.zhToGlobal('暂无成员，无法进行均分'),
          icon: 'none'
        });
        return;
      }

      this.$refs.equalProbabilityPopup.open({
        memberList: this.memberList,
        creatorUserId: this.creatorUserId
      });
    },

    // 均分概率确认回调
    onEqualProbabilityConfirm(data) {
      const { memberProbabilities } = data;

      // 调用后端API进行批量更新
      this.$request.post('/project-make-friends-web/invitationRelation/updateMembersProbabilityBatch', {
        data: {
          invitationGroupId: this.invitationGroupId,
          memberProbabilities: memberProbabilities
        }
      }).then(res => {
        if (res.state === 2000) {
          uni.showToast({
            title: this.$i18n.zhToGlobal('均分成功'),
            icon: 'success'
          });
          this.refreshData(); // 刷新数据
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('均分失败'),
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('均分概率失败:', err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
      });
    },

    // 生成小程序码
    generateMiniProgramCode() {
      uni.showLoading({
        title: this.$i18n.zhToGlobal('生成中...')
      });

      // 跳转到小程序码展示页面
      setTimeout(() => {
        uni.hideLoading();
        uni.navigateTo({
          url: `/pages/project-make-friends-pages/member-partner/miniprogram-code?invitationGroupId=${this.invitationGroupId}`
        });
      }, 500);
    },

    // 获取组信息
    getGroupInfo() {
      this.$request.post('/project-make-friends-web/invitationGroup/getById', {
        data: {
          id: this.invitationGroupId
        }
      }).then(res => {
        if (res.state === 2000) {
          this.creatorUserId = res.data.creatorUserAccountId;
        }
      }).catch(err => {
        console.error('获取组信息失败:', err);
      });
    },

    // 检查当前用户是否为协作人
    checkCurrentUserCollaborator() {
      this.$request.post('/project-make-friends-web/invitationRelation/isCurrentUserCollaborator', {
        data: {
          invitationGroupId: this.invitationGroupId
        }
      }).then(res => {
        if (res.state === 2000) {
          this.isCurrentUserCollaborator = res.data;
        }
      }).catch(err => {
        console.error('检查协作人身份失败:', err);
        this.isCurrentUserCollaborator = false;
      });
    },

    // 联系用户
    contactUser(member) {
      // 这里可以实现联系功能，比如跳转到聊天页面或拨打电话
      uni.showToast({
        title: '联系功能待实现',
        icon: 'none'
      });
    },



    // 编辑成员概率
    editMemberProbability(member) {
      // 获取创建人的当前概率
      const creatorMember = this.memberList.find(m => m.userAccountId === this.creatorUserId);
      const availableProbability = creatorMember ? (creatorMember.probability * 100) + (member.probability * 100) : (member.probability * 100);

      this.currentEditMember = member;
      this.$refs.memberProbabilityPopup.open({
        user: member.userInfo,
        availableProbability: availableProbability,
        isEdit: true,
        currentProbability: member.probability * 100
      });
    },

    // 成员概率确认回调
    onMemberProbabilityConfirm(data) {
      if (this.currentEditMember) {
        const newProbability = parseFloat(data.probability) / 100; // 转换为0-1之间的小数

        this.$request.post('/project-make-friends-web/invitationRelation/updateMemberProbabilityWithCreatorAdjustment', {
          data: {
            invitationGroupId: this.invitationGroupId,
            userAccountId: this.currentEditMember.userAccountId,
            probability: newProbability,
            creatorUserAccountId: this.creatorUserId
          }
        }).then(res => {
          if (res.state === 2000) {
            uni.showToast({
              title: this.$i18n.zhToGlobal('修改成功'),
              icon: 'success'
            });
            // 刷新数据以获取最新的概率分配
            this.refreshData();
          } else {
            uni.showToast({
              title: res.msg || this.$i18n.zhToGlobal('修改失败'),
              icon: 'none'
            });
          }
        }).catch(err => {
          console.error('修改概率失败:', err);
          // 检查错误详情
          let errorMessage = this.$i18n.zhToGlobal('网络异常，请稍后重试');
          if (err.response && err.response.data && err.response.data.msg) {
            errorMessage = err.response.data.msg;
          } else if (err.message) {
            errorMessage = err.message;
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        });

        this.currentEditMember = null;
      }
    },

    // 删除成员（直接删除，无确认框）
    doDeleteMember(member) {
      this.$request.post('/project-make-friends-web/invitationRelation/removeMemberFromGroupWithCreatorAdjustment', {
        data: {
          invitationGroupId: this.invitationGroupId,
          userAccountId: member.userAccountId,
          creatorUserAccountId: this.creatorUserId
        }
      }).then(res => {
        if (res.state === 2000) {
          uni.showToast({
            title: this.$i18n.zhToGlobal('删除成功'),
            icon: 'success'
          });
          // 刷新数据以获取最新的概率分配
          this.refreshData();
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('删除失败'),
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error('删除成员失败:', err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.gradient-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #07C160, #e6f7ef);
  z-index: -2;
}

.back {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: transparent; // 修改为透明
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
}

.header-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}

.content-box {
  padding-bottom: 350rpx;
  /* 进一步增加底部内边距，确保内容不被遮挡 */
  box-sizing: border-box;
  overflow-x: hidden;
  width: 100%;
  position: relative;
  z-index: 1;
}

/* 二维码区域 */
.qrcode-section {
  background-color: rgba(7, 193, 96, 0.8);
  padding: 40rpx 20rpx;
  text-align: center;
  margin: 20rpx auto;
  /* 添加auto使区域在页面中居中 */
  border-radius: 12rpx;
  backdrop-filter: blur(5px);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 确保子元素在水平方向居中 */
  max-width: 710rpx;
  /* 确保在大屏幕上不会过宽 */

  .qrcode-container {
    background-color: #FFFFFF;
    padding: 15rpx;
    /* 添加一些内边距 */
    border-radius: 8rpx;
    /* 更小的圆角 */
    margin-bottom: 20rpx;
    width: 310rpx;
    /* 增加容器宽度 */
    height: 310rpx;
    /* 增加容器高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    /* 确保内边距不会改变总宽高 */
  }

  .centered-qrcode {
    max-width: 280rpx !important;
    /* 限制最大宽度 */
    max-height: 280rpx !important;
    /* 限制最大高度 */
    width: auto !important;
    /* 自动宽度 */
    height: auto !important;
    /* 自动高度 */
  }

  .qrcode-loading {
    color: #999999;
    font-size: 28rpx;
    text-align: center;
  }

  .qrcode-tip {
    color: #FFFFFF;
    font-size: 28rpx;
    line-height: 1.5;
    padding: 0 20rpx;
    text-align: center;
    /* 确保文本居中 */
    width: 100%;
    /* 占满父容器宽度 */
  }
}

/* 参与总人数区域 - 移除边框和阴影 */
.member-count-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  margin: 0 20rpx 20rpx 20rpx;
  background-color: transparent;
  /* 移除背景色 */
  border-radius: 0;
  box-shadow: none;
  /* 移除阴影 */
  backdrop-filter: none;

  .member-count-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
  }

  .refresh-btn {
    padding: 10rpx 20rpx;
    background-color: rgba(240, 240, 240, 0.7);
    border-radius: 20rpx;
    font-size: 28rpx;
    color: #666666;

    &:active {
      opacity: 0.7;
    }
  }
}

/* 搜索框样式 - 与user-select.vue一致 */
.box-search {
  width: calc(100% - 40rpx);
  background-color: transparent;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin: 20rpx auto;
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8rpx;
  padding: 10rpx;
  backdrop-filter: blur(5px);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 搜索按钮样式 */
.search-btn {
  background-color: #07C160;
  height: 30px;
  line-height: 30px;
  color: #ffffff;

  &[disabled] {
    background-color: #cccccc;
    color: #ffffff;
    opacity: 0.6;
  }
}

/* 主容器样式 - 增加内边距并移除边框 */
.box {
  position: relative;
  padding: 0 20rpx;
  /* 增加左右内边距 */
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;

  .box-item {
    margin-bottom: 30rpx;
    width: 100%;
    box-sizing: border-box;

    .box-item-card {
      position: relative;
      padding: 40rpx;
      border-radius: 24rpx;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      border: 1rpx solid #eaeaea;
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
      backdrop-filter: blur(5px);

      /* 卡片上半部分：名称+ID 和 概率值 */
      .card-top-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20rpx;
        padding-bottom: 20rpx;
        border-bottom: 1rpx solid #f0f0f0;

        .name-id-section {
          flex: 1;
          display: flex;
          align-items: center;

          .member-avatar {
            margin-right: 15rpx;
            flex-shrink: 0;
          }

          .member-info {
            flex: 1;

            .member-name {
              font-size: 32rpx;
              font-weight: bold;
              color: #333333;
              margin-bottom: 8rpx;
              letter-spacing: 1px;
            }

            .member-id {
              font-size: 26rpx;
              color: #999999;
            }
          }
        }

        .probability-section {
          text-align: right;
          min-width: 120rpx;
          display: flex;
          align-items: center;
          gap: 8rpx;

          .probability-label {
            font-size: 28rpx;
            color: #333333;
          }

          .probability-value {
            font-size: 52rpx;
            font-weight: bold;
            color: #e74c3c;
          }
        }
      }

      /* 卡片下半部分：联系按钮 和 修改删除按钮 */
      .card-bottom-section {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .contact-section {
          flex: 1;

          .contact-btn {
            background-color: transparent;
            color: #333333;
            padding: 16rpx 32rpx;
            border-radius: 25rpx;
            font-size: 28rpx;
            text-align: center;
            max-width: 200rpx;

            &:active {
              opacity: 0.8;
            }
          }
        }

        .action-section {
          display: flex;
          gap: 20rpx;

          .action-btn-text {
            font-size: 28rpx;
            padding: 8rpx 16rpx;
            cursor: pointer;

            &.edit-btn {
              color: #333333;
            }

            &.delete-btn {
              color: #333333;
            }

            &:active {
              opacity: 0.6;
            }
          }
        }

        .tag-section {
          .creator-tag {
            display: inline-block;
            padding: 6rpx 12rpx;
            background-color: #D4EDDA;
            color: #155724;
            border-radius: 16rpx;
            font-size: 22rpx;
            font-weight: bold;
          }

          .collaborator-tag {
            display: inline-block;
            padding: 6rpx 12rpx;
            background-color: #D1ECF1;
            color: #0C5460;
            border-radius: 16rpx;
            font-size: 22rpx;
            font-weight: bold;
          }
        }
      }


    }
  }
}

.box-item:active {
  opacity: .8;
}

.box-item-card:active {
  background-color: #f9f9f9;
}

/* 提示语样式 */
.empty-tip {
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  margin: 100rpx 0;
}

.load-more-tip {
  text-align: center;
  color: #999999;
  font-size: 24rpx;
  padding: 20rpx;
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 15rpx 20rpx;
  /* 减小上下内边距 */
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  /* 减小按钮间距 */
  box-sizing: border-box;
  backdrop-filter: blur(10px);

  .action-btn {
    width: 100%;
    padding: 20rpx;
    /* 减小按钮内边距 */
    border-radius: 25rpx;
    text-align: center;
    font-size: 30rpx;
    font-weight: bold;
    box-sizing: border-box;
    margin-bottom: 8rpx;
    /* 减小按钮底部间距 */

    &:active {
      opacity: 0.8;
    }

    &.wechat-style-btn {
      background-color: #FFFFFF;
      color: #07C160;
      border: 2rpx solid #07C160;
    }
  }
}



// 暗黑模式适配
.container-mode-color-scheme-dark {
  .gradient-background {
    background: linear-gradient(to bottom, #059E4F, #1a2e25);
  }

  .back {
    background-color: transparent;
  }

  .qrcode-section {
    background-color: rgba(5, 158, 79, 0.8);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  }

  .member-count-section {
    background-color: transparent;

    .member-count-text {
      color: #FFFFFF;
    }

    .refresh-btn {
      background-color: rgba(51, 51, 51, 0.8);
      color: #AAAAAA;
    }
  }

  .search-box,
  .bottom-actions {
    background-color: rgba(31, 31, 31, 0.85);
    color: #AAAAAA;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  }

  .search-input {
    color: #FFFFFF;
  }

  .box {
    .box-item {
      .box-item-card {
        background-color: rgba(31, 31, 31, 0.85);
        border: 1rpx solid #333333;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);

        &:active {
          background-color: rgba(42, 42, 42, 0.85);
        }

        .card-top-section {
          border-bottom: 1rpx solid #333333;

          .name-id-section {
            .member-info {
              .member-name {
                color: #ccc;
              }

              .member-id {
                color: #AAAAAA;
              }
            }
          }

          .probability-section {
            .probability-label {
              color: #AAAAAA;
            }

            .probability-value {
              color: #e74c3c;
            }
          }
        }

        .card-bottom-section {
          .action-section {
            .action-btn-text {
              &.edit-btn {
                color: #AAAAAA;
              }

              &.delete-btn {
                color: #AAAAAA;
              }
            }
          }

          .tag-section {
            .creator-tag {
              background-color: #2D5A3D;
              color: #A8D5BA;
            }

            .collaborator-tag {
              background-color: #1E3A3A;
              color: #7FCDCD;
            }
          }
        }
      }
    }
  }

  .bottom-actions {
    background-color: rgba(31, 31, 31, 0.9);
    border-top: 1rpx solid #333333;

    .action-btn {
      &.wechat-style-btn {
        background-color: rgba(31, 31, 31, 0.9);
        color: #07C160;
        border: 2rpx solid #07C160;
      }
    }
  }
}

// PC端适配
.container-mode-device-pc {
  .content-box {
    max-width: 800rpx;
    margin: 0 auto;

    .box {
      max-width: 800rpx;
      margin: 0 auto;
    }
  }

  .header-box {
    max-width: 800rpx;
    margin: 0 auto;
  }

  .bottom-actions {
    max-width: 800rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 800rpx;
  }
}
</style>