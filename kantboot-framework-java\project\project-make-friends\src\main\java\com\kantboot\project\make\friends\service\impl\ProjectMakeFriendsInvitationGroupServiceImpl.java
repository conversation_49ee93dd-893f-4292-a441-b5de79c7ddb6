package com.kantboot.project.make.friends.service.impl;

import com.kantboot.project.make.friends.dao.repository.ProjectMakeFriendsInvitationGroupRepository;
import com.kantboot.project.make.friends.domain.dto.ProjectMakeFriendsInvitationGroupWithCreatorDTO;
import com.kantboot.project.make.friends.domain.entity.ProjectMakeFriendsInvitationGroup;
import com.kantboot.project.make.friends.domain.entity.ProjectMakeFriendsInvitationRelation;
import com.kantboot.project.make.friends.service.IProjectMakeFriendsInvitationGroupCollaboratorService;
import com.kantboot.project.make.friends.service.IProjectMakeFriendsInvitationGroupService;
import com.kantboot.project.make.friends.service.IProjectMakeFriendsInvitationRelationService;
import com.kantboot.user.account.domain.entity.UserAccount;
import com.kantboot.user.account.service.IUserAccountService;
import com.kantboot.util.jpa.param.PageParam;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 邀约协作组服务实现类
 * <AUTHOR>
 */
@Service
public class ProjectMakeFriendsInvitationGroupServiceImpl implements IProjectMakeFriendsInvitationGroupService {

    @Resource
    private ProjectMakeFriendsInvitationGroupRepository repository;

    @Resource
    private IUserAccountService userAccountService;

    @Resource
    private IProjectMakeFriendsInvitationRelationService invitationRelationService;

    @Resource
    private IProjectMakeFriendsInvitationGroupCollaboratorService collaboratorService;

    @Override
    public Page<ProjectMakeFriendsInvitationGroup> getList(PageParam<Object> pageParam, String searchText) {
        PageRequest pageRequest = PageRequest.of(pageParam.getPageNumber() - 1, pageParam.getPageSize());

        if (searchText != null && !searchText.trim().isEmpty()) {
            // 根据组名模糊查询
            return repository.findByGroupNameContaining(searchText.trim(), pageRequest);
        } else {
            // 查询所有数据
            return repository.findAllOrderByGmtCreateDesc(pageRequest);
        }
    }

    @Override
    public Page<ProjectMakeFriendsInvitationGroupWithCreatorDTO> getListWithCreatorInfo(PageParam<Object> pageParam, String searchText) {
        // 先获取邀约组列表
        Page<ProjectMakeFriendsInvitationGroup> groupPage = getList(pageParam, searchText);

        // 转换为包含创建人信息的DTO
        return groupPage.map(group -> {
            ProjectMakeFriendsInvitationGroupWithCreatorDTO dto = new ProjectMakeFriendsInvitationGroupWithCreatorDTO();

            // 复制基本信息
            dto.setId(group.getId())
               .setGmtCreate(group.getGmtCreate())
               .setGmtModified(group.getGmtModified())
               .setGroupName(group.getGroupName())
               .setAgreementUserIds(group.getAgreementUserIds())
               .setCreatorUserAccountId(group.getCreatorUserAccountId())
               .setCreationConfirmStatus(group.getCreationConfirmStatus())
               .setDescription(group.getDescription())
               .setRemark(group.getRemark());

            // 获取创建人信息
            if (group.getCreatorUserAccountId() != null) {
                UserAccount creator = userAccountService.getById(group.getCreatorUserAccountId());
                if (creator != null) {
                    dto.setCreatorNickname(creator.getNickname())
                       .setCreatorPhone(creator.getPhone())
                       .setCreatorPhoneAreaCode(creator.getPhoneAreaCode())
                       .setCreatorFileIdOfAvatar(creator.getFileIdOfAvatar());
                }
            }

            return dto;
        });
    }

    @Override
    public ProjectMakeFriendsInvitationGroup getById(Long id) {
        return repository.findById(id).orElse(null);
    }

    @Override
    public ProjectMakeFriendsInvitationGroup save(ProjectMakeFriendsInvitationGroup entity) {
        return repository.save(entity);
    }

    @Override
    public void deleteById(Long id) {
        repository.deleteById(id);
    }

    @Override
    public Page<ProjectMakeFriendsInvitationGroup> getMyList(PageParam<Object> pageParam, String searchText) {
        // 获取当前登录用户ID
        Long currentUserId = userAccountService.getSelfId();

        PageRequest pageRequest = PageRequest.of(pageParam.getPageNumber() - 1, pageParam.getPageSize());

        if (searchText != null && !searchText.trim().isEmpty()) {
            // 根据创建者ID和组名模糊查询
            return repository.findByCreatorUserAccountIdAndGroupNameContaining(currentUserId, searchText.trim(), pageRequest);
        } else {
            // 根据创建者ID查询
            return repository.findByCreatorUserAccountId(currentUserId, pageRequest);
        }
    }

    @Override
    public Page<ProjectMakeFriendsInvitationGroupWithCreatorDTO> getMyCreatedOrCollaboratedListWithCreatorInfo(PageParam<Object> pageParam, String searchText, String sortType) {
        // 获取当前登录用户ID
        Long currentUserId = userAccountService.getSelfId();

        PageRequest pageRequest = PageRequest.of(pageParam.getPageNumber() - 1, pageParam.getPageSize());

        // 获取用户在邀约关系表中标记为协作人的邀约组ID列表
        List<Long> collaboratedGroupIds = invitationRelationService.getInvitationGroupIdsByCollaboratorFromRelation(currentUserId);

        // 如果协作组ID列表为空，添加一个不存在的ID以避免SQL错误
        if (collaboratedGroupIds.isEmpty()) {
            collaboratedGroupIds.add(-1L);
        }

        // 构建查询条件：创建人是自己 OR 邀约组ID在协作列表中
        Page<ProjectMakeFriendsInvitationGroup> groupPage;
        if (searchText != null && !searchText.trim().isEmpty()) {
            // 有搜索条件时
            groupPage = repository.findByCreatorUserAccountIdOrIdInAndGroupNameContaining(
                currentUserId, collaboratedGroupIds, searchText.trim(), pageRequest);
        } else {
            // 无搜索条件时
            groupPage = repository.findByCreatorUserAccountIdOrIdIn(currentUserId, collaboratedGroupIds, pageRequest);
        }

        // 转换为包含创建人信息的DTO
        List<ProjectMakeFriendsInvitationGroupWithCreatorDTO> dtoList = groupPage.getContent().stream()
            .map(group -> {
                // 获取创建人信息
                UserAccount creator = userAccountService.getById(group.getCreatorUserAccountId());

                ProjectMakeFriendsInvitationGroupWithCreatorDTO dto = new ProjectMakeFriendsInvitationGroupWithCreatorDTO();
                dto.setId(group.getId());
                dto.setGmtCreate(group.getGmtCreate());
                dto.setGmtModified(group.getGmtModified());
                dto.setGroupName(group.getGroupName());
                dto.setDescription(group.getDescription());
                dto.setCreatorUserAccountId(group.getCreatorUserAccountId());
                dto.setCreationConfirmStatus(group.getCreationConfirmStatus());

                if (creator != null) {
                    dto.setCreatorNickname(creator.getNickname());
                    dto.setCreatorPhone(creator.getPhone());
                    dto.setCreatorFileIdOfAvatar(creator.getFileIdOfAvatar());
                }

                // 获取当前登录用户在该邀约组中的概率值
                try {
                    ProjectMakeFriendsInvitationRelation relation = invitationRelationService.getByInvitationGroupIdAndUserAccountId(group.getId(), currentUserId);
                    if (relation != null && relation.getProbability() != null) {
                        // 将概率转换为百分比形式（0.25 -> 25）
                        dto.setProbability(relation.getProbability().multiply(new BigDecimal("100")));
                    } else {
                        dto.setProbability(BigDecimal.ZERO);
                    }
                } catch (Exception e) {
                    // 如果获取概率失败，设置为0
                    dto.setProbability(BigDecimal.ZERO);
                }

                return dto;
            })
            .collect(Collectors.toList());

        // 根据排序类型进行排序
        if ("probability".equals(sortType)) {
            // 按概率从小到大排序
            dtoList.sort(Comparator.comparing(ProjectMakeFriendsInvitationGroupWithCreatorDTO::getProbability, Comparator.nullsLast(Comparator.naturalOrder())));
        } else {
            // 按时间从近到远排序（默认）
            dtoList.sort(Comparator.comparing(ProjectMakeFriendsInvitationGroupWithCreatorDTO::getGmtCreate, Comparator.nullsLast(Comparator.reverseOrder())));
        }

        return new PageImpl<>(dtoList, pageRequest, groupPage.getTotalElements());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectMakeFriendsInvitationGroup createGroupWithMembers(String groupName, String description, List<Long> userAccountIds, BigDecimal probability) {
        // 获取当前登录用户ID
        Long currentUserId = userAccountService.getSelfId();

        // 创建邀约协作组
        ProjectMakeFriendsInvitationGroup group = new ProjectMakeFriendsInvitationGroup();
        group.setGroupName(groupName);
        group.setDescription(description);
        group.setCreatorUserAccountId(currentUserId);
        group.setCreationConfirmStatus(0); // 待所有人确认

        // 保存邀约组
        group = repository.save(group);

        // 添加成员到邀约组（包括创建者）
        if (userAccountIds != null && !userAccountIds.isEmpty()) {
            // 确保创建者在成员列表中
            if (!userAccountIds.contains(currentUserId)) {
                userAccountIds.add(currentUserId);
            }

            // 批量添加成员，标记为初次创建确认
            invitationRelationService.addMembersToGroup(group.getId(), userAccountIds, probability, 1);
        }

        return group;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectMakeFriendsInvitationGroup createNewInvitationGroup(List<Long> userAccountIds) {
        // 获取当前登录用户ID
        Long currentUserId = userAccountService.getSelfId();

        // 生成默认组名
        String defaultGroupName = "共同邀约-" + System.currentTimeMillis();

        // 确保创建者在成员列表中
        if (userAccountIds == null || userAccountIds.isEmpty()) {
            userAccountIds = List.of(currentUserId);
        } else if (!userAccountIds.contains(currentUserId)) {
            userAccountIds.add(currentUserId);
        }

        // 创建邀约组，默认概率为0
        return createGroupWithMembers(defaultGroupName, "新增共同邀约人", userAccountIds, BigDecimal.ZERO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectMakeFriendsInvitationGroup createNewInvitationGroupWithProbability(List<IProjectMakeFriendsInvitationGroupService.UserProbabilityInfo> usersWithProbability, Integer defaultParticipationStatus) {
        // 获取当前登录用户ID
        Long currentUserId = userAccountService.getSelfId();

        // 生成默认组名
        String defaultGroupName = "共同邀约-" + System.currentTimeMillis();

        // 创建邀约协作组
        ProjectMakeFriendsInvitationGroup group = new ProjectMakeFriendsInvitationGroup();
        group.setGroupName(defaultGroupName);
        group.setDescription("新增共同邀约人");
        group.setCreatorUserAccountId(currentUserId);
        group.setCreationConfirmStatus(1); // 直接设置为已确认创建

        // 保存邀约组
        group = repository.save(group);

        // 添加成员到邀约组，使用各自的概率和默认参与状态
        if (usersWithProbability != null && !usersWithProbability.isEmpty()) {
            for (IProjectMakeFriendsInvitationGroupService.UserProbabilityInfo userInfo : usersWithProbability) {
                invitationRelationService.addMemberToGroupWithStatus(
                    group.getId(),
                    userInfo.getUserAccountId(),
                    userInfo.getProbability(),
                    0, // 不是初次创建确认
                    defaultParticipationStatus != null ? defaultParticipationStatus : 1 // 默认已同意
                );
            }
        }

        return group;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProjectMakeFriendsInvitationGroup createNewInvitationGroupWithProbabilityAndName(String groupName, List<IProjectMakeFriendsInvitationGroupService.UserProbabilityInfo> usersWithProbability, Integer defaultParticipationStatus) {
        // 获取当前登录用户ID
        Long currentUserId = userAccountService.getSelfId();

        // 创建邀约协作组
        ProjectMakeFriendsInvitationGroup group = new ProjectMakeFriendsInvitationGroup();
        group.setGroupName(groupName);
        group.setDescription("新增共同邀约人");
        group.setCreatorUserAccountId(currentUserId);
        group.setCreationConfirmStatus(1); // 直接设置为已确认创建

        // 保存邀约组
        group = repository.save(group);

        // 添加成员到邀约组，使用各自的概率和默认参与状态
        if (usersWithProbability != null && !usersWithProbability.isEmpty()) {
            for (IProjectMakeFriendsInvitationGroupService.UserProbabilityInfo userInfo : usersWithProbability) {
                invitationRelationService.addMemberToGroupWithStatus(
                    group.getId(),
                    userInfo.getUserAccountId(),
                    userInfo.getProbability(),
                    0, // 不是初次创建确认
                    defaultParticipationStatus != null ? defaultParticipationStatus : 1 // 默认已同意
                );
            }
        }

        return group;
    }

    @Override
    public Long getUserIdByGroupProbability(Long invitationGroupId) {
        // 获取邀约组的所有成员及其概率
        List<ProjectMakeFriendsInvitationRelation> members = invitationRelationService.getAllMembersByGroupId(invitationGroupId);

        if (members == null || members.isEmpty()) {
            throw new RuntimeException("邀约组中没有成员");
        }

        // 计算总概率
        BigDecimal totalProbability = members.stream()
            .map(ProjectMakeFriendsInvitationRelation::getProbability)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalProbability.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("邀约组中所有成员的概率总和为0");
        }

        // 生成0到1之间的随机数
        double random = Math.random();
        BigDecimal randomValue = BigDecimal.valueOf(random).multiply(totalProbability);

        // 根据概率选择用户
        BigDecimal currentSum = BigDecimal.ZERO;
        for (ProjectMakeFriendsInvitationRelation member : members) {
            currentSum = currentSum.add(member.getProbability());
            if (randomValue.compareTo(currentSum) <= 0) {
                return member.getUserAccountId();
            }
        }

        // 如果由于精度问题没有选中任何用户，返回最后一个用户
        return members.get(members.size() - 1).getUserAccountId();
    }
}
