<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>project</artifactId>
    <version>ARISTOTLE-0.1</version>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>project-make-friends</artifactId>
  <version>ARISTOTLE-0.1</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>user-all</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>tool-state-area</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>tool-ip</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>fp-community</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>starter</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>api-wechat-pay</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.32</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
