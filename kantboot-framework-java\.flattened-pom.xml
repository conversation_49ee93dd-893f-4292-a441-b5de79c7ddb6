<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.4.3</version>
    <relativePath></relativePath>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>parent</artifactId>
  <version>ARISTOTLE-0.1</version>
  <packaging>pom</packaging>
  <name>parent</name>
  <description>parent</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>dependencies</module>
    <module>util</module>
    <module>system</module>
    <module>global</module>
    <module>starter-parent</module>
    <module>user</module>
    <module>functional</module>
    <module>api</module>
    <module>official-plugin</module>
    <module>tool</module>
    <module>front-end-manage</module>
    <module>project</module>
    <module>socket</module>
    <module>fp</module>
  </modules>
  <properties>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <java.version>21</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <spring-boot.version>3.4.3</spring-boot.version>
    <kantboot.version>ARISTOTLE-0.1</kantboot.version>
    <revision>${kantboot.version}</revision>
  </properties>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>1.3.0</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
            <configuration>
              <updatePomFile>true</updatePomFile>
              <flattenMode>resolveCiFriendliesOnly</flattenMode>
              <pomElements>
                <parent>expand</parent>
                <distributionManagement>remove</distributionManagement>
                <repositories>remove</repositories>
              </pomElements>
            </configuration>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <inherited>true</inherited>
      </plugin>
    </plugins>
  </build>
</project>
