E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\api\api-google\api-google-translate\src\main\java\com\kantboot\api\google\translate\dao\repository\ApiGoogleTranslateTextRepository.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\api\api-google\api-google-translate\src\main\java\com\kantboot\api\google\translate\domain\entity\ApiGoogleTranslateText.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\api\api-google\api-google-translate\src\main\java\com\kantboot\api\google\translate\exception\ApiTranslateException.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\api\api-google\api-google-translate\src\main\java\com\kantboot\api\google\translate\service\IApiGoogleTranslateService.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\api\api-google\api-google-translate\src\main\java\com\kantboot\api\google\translate\service\impl\ApiGoogleTranslateServiceImpl.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\api\api-google\api-google-translate\src\main\java\com\kantboot\api\google\translate\util\GoogleTranslateUtil.java
E:\gitData\work\bussine\receiving-orders_ll\kant-boot\kantboot-framework-java\api\api-google\api-google-translate\src\main\java\com\kantboot\api\google\translate\web\controller\GoogleTranslateController.java
