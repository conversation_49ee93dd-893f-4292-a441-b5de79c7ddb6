<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>functional</artifactId>
    <version>ARISTOTLE-0.1</version>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>functional-all</artifactId>
  <version>ARISTOTLE-0.1</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-file</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-translate</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-email</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-sms</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-template</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-verification-code</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-message</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-pay</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-chat</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-notify</artifactId>
      <version>${revision}</version>
    </dependency>
  </dependencies>
</project>
