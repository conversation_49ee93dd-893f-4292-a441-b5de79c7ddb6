<template>
  <view>
    <view
        class="header"
        id="headerInMemberPartner">
      <kt-nav-bar :title="$i18n.zhToGlobal('概率邀约')" :showBack="true"></kt-nav-bar>
    </view>
    <view>
      <view class="box">
        <!-- 轮播图 -->
        <view class="carousel-box" v-if="swiperList.length > 0">
          <swiper class="swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
                  :circular="true">
            <swiper-item v-for="(item, index) in swiperList" :key="index" @click="onCarouselClick(item)">
              <view class="swiper-item">
                <image :src="$kt.file.visit(item.fileIdOfImage)" mode="aspectFill" class="swiper-image"></image>
                <view class="swiper-title" v-if="item.title">{{ item.title }}</view>
              </view>
            </swiper-item>
          </swiper>
        </view>

        <!-- 搜索框 -->
        <view class="box-search">
          <view class="box-search-input">
            <u-input :placeholder="$i18n.zhToGlobal('输入组名搜索')" @confirm="searchGroups" v-model="searchText">
              <template slot="suffix">
                <button @tap="searchGroups" class="search-btn" :disabled="!searchText.trim()">搜索</button>
              </template>
            </u-input>
          </view>
        </view>

        <!-- 排序功能 -->
        <view class="sort-section">
          <view class="sort-label">排序</view>
          <view class="sort-buttons">
            <view class="sort-btn" :class="{ active: sortType === 'probability' }" @click="setSortType('probability')">
              从小到大
            </view>
            <view class="sort-btn" :class="{ active: sortType === 'time' }" @click="setSortType('time')">
              从近到远
            </view>
          </view>
        </view>

        <!-- 邀约协作组列表 -->
        <view v-if="groupList.length === 0 && !isLoading" class="empty-tip">
          {{ $i18n.zhToGlobal('暂无邀约协作组') }}
        </view>

        <!-- 邀约协作组卡片列表 -->
        <view class="box-item" v-for="item in groupList" :key="item.id" @click="viewGroupDetail(item)">
          <view class="box-item-card">
            <!-- 上半部分：名称+ID 和 概率值 -->
            <view class="card-top-section">
              <view class="name-id-section">
                <view class="group-name">{{ item.groupName }}</view>
                <view class="group-id">ID: {{ item.creatorUserAccountId }}</view>
              </view>
              <view class="probability-section">
                <view class="probability-label">概率</view>
                <view class="probability-value">{{ item.probability || '0' }}%</view>
              </view>
            </view>

            <!-- 下半部分：联系按钮 和 修改删除按钮 -->
            <view class="card-bottom-section">
              <view class="contact-section">
                <view class="contact-btn" @click.stop="contactUser(item)">
                  联系他（她）
                </view>
              </view>
              <view class="action-section" @click.stop v-if="isCreator(item)">
                <view class="action-btn-text edit-btn" @click="editGroup(item)">
                  修改
                </view>
                <view class="action-btn-text delete-btn" @click="doDeleteGroup(item.id)">
                  删除
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <u-loading-icon v-if="isLoading" mode="circle" size="50rpx"></u-loading-icon>
        <view v-if="isBottom" style="text-align: center; color: #999999; font-size: 24rpx;">
          {{ $kt.i18n.zhToGlobal('没有更多了') }}
        </view>
        <view style="height: 50rpx;"></view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-btn-box">
      <view class="btn-item add-btn" @click="toAddGroup">
        <text>{{ $i18n.zhToGlobal('新增共同邀约人') }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import KtImage from "../../../uni_modules/kantboot/components/kt-image/kt-image.vue";
import KtAvatar from "../../../uni_modules/kantboot/components/kt-avatar/kt-avatar.vue";

export default {
  components: {KtImage, KtAvatar},
  data() {
    return {
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      },
      searchText: '',
      groupList: [],
      pageNum: 1,
      pageSize: 10,
      isLoading: false,
      isBottom: false,
      // 轮播图数据
      swiperList: [
      ],
      currentUserId: null, // 当前用户ID
      sortType: 'probability' // 排序类型：probability-概率排序，time-时间排序
    }
  },
  onLoad() {
    this.getCurrentUserId();
    this.getInitList();
    this.getCarouselData();
  },
  methods: {
    // 获取当前用户ID
    getCurrentUserId() {
      this.currentUserId = this.$kt.userAccount.getSelf().id;
    },

    // 判断是否为创建人
    isCreator(item) {
      return this.currentUserId && item.creatorUserAccountId === this.currentUserId;
    },

    // 初始加载邀约协作组列表
    getInitList() {
      this.isLoading = true;
      this.pageNum = 1;
      this.isBottom = false;

      this.$request.post("/project-make-friends-web/invitationGroup/getMyCreatedOrCollaboratedListWithCreatorInfo", {
        data: {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          searchText: this.searchText,
          sortType: this.sortType
        }
      }).then(res => {
        if (res.state === 2000) {
          this.groupList = res.data || [];
          if (this.groupList.length < this.pageSize) {
            this.isBottom = true;
          }
        } else {
          uni.showToast({
            title: res.msg || '获取邀约协作组列表失败',
            icon: 'none'
          });
        }
        this.isLoading = false;
      }).catch(err => {
        console.error(err);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
        this.isLoading = false;
      });
    },

    // 搜索邀约协作组
    searchGroups() {
      this.getInitList();
    },

    // 设置排序类型
    setSortType(type) {
      this.sortType = type;
      this.getInitList(); // 重新加载数据
    },

    // 联系用户
    contactUser(item) {
      // 这里可以实现联系功能，比如跳转到聊天页面或拨打电话
      uni.showToast({
        title: '联系功能待实现',
        icon: 'none'
      });
    },

    // 编辑组（跳转详情）
    editGroup(item) {
      uni.navigateTo({
        url: `/pages/project-make-friends-pages/member-partner/member-partner-content?invitationGroupId=${item.id}`
      });
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待所有人确认',
        1: '已确认创建'
      };
      return this.$i18n.zhToGlobal(statusMap[status] || '未知状态');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: 'status-pending',
        1: 'status-confirmed'
      };
      return classMap[status] || 'status-unknown';
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },



    // 执行删除操作
    doDeleteGroup(id) {
      this.$request.post("/project-make-friends-web/invitationGroup/deleteById", {
        data: { id: id }
      }).then(res => {
        if (res.state === 2000) {
          uni.showToast({
            title: this.$i18n.zhToGlobal('删除成功'),
            icon: 'success'
          });
          this.getInitList(); // 重新加载列表
        } else {
          uni.showToast({
            title: res.msg || '删除失败',
            icon: 'none'
          });
        }
      }).catch(err => {
        console.error(err);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      });
    },
    // 获取轮播图数据
    getCarouselData() {
      this.$request.post("/fp-carousel-web/carousel/getByTypeCode", {
        data: {
          typeCode: "member-partner" // 会员转移页面轮播图类型编码
        }
      }).then(res => {
        if (res.state === 2000 && res.data && res.data.length > 0) {
          this.swiperList = res.data.map(item => ({
            fileIdOfImage: item.fileIdOfImage,
            title: item.title,
            link: item.link,
            id: item.id
          }));
        }
      }).catch(err => {
        console.error('获取轮播图数据失败:', err);
        // 保持默认的轮播图数据，不影响页面正常显示
      });
    },


    // 查看组详情
    viewGroupDetail(item) {
      uni.navigateTo({
        url: `/pages/project-make-friends-pages/member-partner/member-partner-content?invitationGroupId=${item.id}`
      });
    },

    // 新增共同邀约人（创建新组）
    toAddGroup() {
      uni.navigateTo({
        url: '/pages/project-make-friends-pages/member-partner/user-select?mode=createGroup'
      });
    },

    // 轮播图点击事件
    onCarouselClick(item) {
      if (item.link) {
        // 如果有链接，可以跳转到相应页面
        if (item.link.startsWith('http')) {
          // 外部链接
          uni.navigateTo({
            url: `/pages/common/webview?url=${encodeURIComponent(item.link)}`
          });
        } else {
          // 内部页面跳转
          uni.navigateTo({
            url: item.link
          });
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.back {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #F0F0F0;
  top: 0;
  left: 0;
  z-index: -1;
}



/* 轮播图样式 */
.swiper-box {
  padding: 20rpx;

  .swiper {
    height: 300rpx;
    border-radius: 12rpx;
    overflow: hidden;

    .swiper-image {
      width: 100%;
      height: 100%;
    }
  }
}

/* 搜索框样式 - 更新为与member-transfer.vue一致 */
.box-search {
  width: calc(100% - 40rpx);
  background-color: #F0F0F0;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin: 20rpx auto;
  margin-bottom: 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  padding: 10rpx;
}

/* 搜索按钮样式 */
.search-btn {
  background-color: #07C160;
  height: 30px;
  line-height: 30px;
  color: #ffffff;

  &[disabled] {
    background-color: #cccccc;
    color: #ffffff;
    opacity: 0.6;
  }
}

/* 排序功能样式 */
.sort-section {
  width: calc(100% - 40rpx);
  margin: 0 auto 30rpx auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;

  .sort-label {
    font-size: 28rpx;
    color: #333333;
    font-weight: bold;
  }

  .sort-buttons {
    display: flex;
    gap: 20rpx;

    .sort-btn {
      padding: 12rpx 24rpx;
      border-radius: 20rpx;
      font-size: 26rpx;
      background-color: #f5f5f5;
      color: #666666;
      transition: all 0.3s ease;

      &.active {
        background-color: #07C160;
        color: #ffffff;
      }

      &:active {
        opacity: 0.8;
      }
    }
  }
}

/* 主容器样式 - 参考member-transfer.vue */
.box {
  position: relative;
  padding: 20rpx;
  box-sizing: border-box;

  .box-item {
    margin-bottom: 30rpx;

    .box-item-card {
      position: relative;
      padding: 40rpx;
      border-radius: 24rpx;
      background-color: #ffffff;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      border: 1rpx solid #eaeaea;

      /* 卡片上半部分：名称+ID 和 概率值 */
      .card-top-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20rpx;
        padding-bottom: 20rpx;
        border-bottom: 1rpx solid #f0f0f0;

        .name-id-section {
          flex: 1;

          .group-name {
            font-size: 32rpx;
            font-weight: bold;
            color: #333333;
            margin-bottom: 8rpx;
            letter-spacing: 1px;
          }

          .group-id {
            font-size: 26rpx;
            color: #999999;
          }
        }

        .probability-section {
          text-align: right;
          min-width: 120rpx;
          display: flex;
          align-items: center;
          gap: 8rpx;

          .probability-label {
            font-size: 28rpx;
            color: #333333;
          }

          .probability-value {
            font-size: 52rpx;
            font-weight: bold;
            color: #e74c3c;
          }
        }
      }

      /* 卡片下半部分：联系按钮 和 修改删除按钮 */
      .card-bottom-section {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .contact-section {
          flex: 1;

          .contact-btn {
            background-color: transparent;
            color: #333333;
            padding: 16rpx 32rpx;
            border-radius: 25rpx;
            font-size: 28rpx;
            text-align: center;
            max-width: 200rpx;

            &:active {
              opacity: 0.8;
            }
          }
        }

        .action-section {
          display: flex;
          gap: 20rpx;

          .action-btn-text {
            font-size: 28rpx;
            padding: 8rpx 16rpx;
            cursor: pointer;

            &.edit-btn {
              color: #333333;
            }

            &.delete-btn {
              color: #333333;
            }

            &:active {
              opacity: 0.6;
            }
          }
        }
      }
    }
  }
}

.box-item:active {
  opacity: .8;
}

.box-item-card:active {
  background-color: #f9f9f9;
}

/* 提示语样式 */
.empty-tip {
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  margin: 100rpx 0;
}


/* 轮播图样式 */
.carousel-box {
  width: calc(100% - 40rpx);
  margin: 0 auto 30rpx auto;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .swiper {
    width: 100%;
    height: 420rpx;

    .swiper-item {
      position: relative;
      width: 100%;
      height: 100%;

      .swiper-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .swiper-title {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
        color: #ffffff;
        padding: 20rpx;
        font-size: 28rpx;
        font-weight: 500;
      }
    }
  }
}

.bottom-btn-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120rpx;
  display: flex;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 999;
  overflow: hidden;

  .btn-item {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;

    &:active {
      opacity: 0.8;
    }
  }

  .add-btn {
    background-color: #07C160;
    color: #FFFFFF;
  }

  .my-groups-btn {
    background-color: #FFFFFF;
    color: #000000;
    border-top: 1rpx solid #EEEEEE;
  }
}

// 暗黑模式适配
.container-mode-color-scheme-dark {
  .back {
    background-color: #191919;
  }

  .search-box {
    background-color: #1F1F1F;
  }

  .carousel-box {
    box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.05);
  }

  .box {
    .box-item {
      .box-item-card {
        background-color: #1F1F1F;
        border: 1rpx solid #333333;

        &:active {
          background-color: #2A2A2A;
        }

        .card-top-section {
          border-bottom: 1rpx solid #333333;

          .name-id-section {
            .group-name {
              color: #ccc;
            }

            .group-id {
              color: #AAAAAA;
            }
          }

          .probability-section {
            .probability-label {
              color: #AAAAAA;
            }

            .probability-value {
              color: #e74c3c;
            }
          }
        }

        .card-bottom-section {
          .action-section {
            .action-btn-text {
              &.edit-btn {
                color: #AAAAAA;
              }

              &.delete-btn {
                color: #AAAAAA;
              }
            }
          }
        }
      }
    }
  }

  .bottom-btn-box {
    background-color: #1F1F1F;
    box-shadow: 0 -2rpx 10rpx rgba(255, 255, 255, 0.05);

    .my-groups-btn {
      background-color: #1F1F1F;
      color: #AAAAAA;
      border-top: 1rpx solid #333333;
    }
  }
}

// PC端适配
.container-mode-device-pc {
  position: relative;
  width: 100%;
  padding: 0;
  margin-left: 240px;
  box-sizing: border-box;

  .box {
    max-width: 800rpx;
    margin: 0 auto;
  }

  .bottom-btn-box {
    width: calc(100% - 240px);
    left: 240px;
  }
}
</style>