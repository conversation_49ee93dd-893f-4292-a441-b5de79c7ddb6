<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>api</artifactId>
    <version>ARISTOTLE-0.1</version>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>api-paypal</artifactId>
  <version>ARISTOTLE-0.1</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.13</version>
    </dependency>
    <dependency>
      <groupId>com.paypal.sdk</groupId>
      <artifactId>paypal-server-sdk</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.egzosn</groupId>
      <artifactId>pay-java-paypal</artifactId>
      <version>2.14.7</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.16.1</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>system-setting</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-pay</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>functional-template</artifactId>
      <version>${revision}</version>
    </dependency>
  </dependencies>
</project>
