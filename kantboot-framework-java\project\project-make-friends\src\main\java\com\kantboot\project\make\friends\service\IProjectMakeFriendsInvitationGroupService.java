package com.kantboot.project.make.friends.service;

import com.kantboot.project.make.friends.domain.dto.ProjectMakeFriendsInvitationGroupWithCreatorDTO;
import com.kantboot.project.make.friends.domain.entity.ProjectMakeFriendsInvitationGroup;
import com.kantboot.util.jpa.param.PageParam;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.util.List;

/**
 * 邀约协作组服务接口
 * <AUTHOR>
 */
public interface IProjectMakeFriendsInvitationGroupService {

    /**
     * 获取邀约协作组列表（分页）
     * @param pageParam 分页参数
     * @param searchText 搜索文本（按组名搜索）
     * @return 分页结果
     */
    Page<ProjectMakeFriendsInvitationGroup> getList(PageParam<Object> pageParam, String searchText);

    /**
     * 获取邀约协作组列表（包含创建人信息）
     * @param pageParam 分页参数
     * @param searchText 搜索文本（按组名搜索）
     * @return 分页结果
     */
    Page<ProjectMakeFriendsInvitationGroupWithCreatorDTO> getListWithCreatorInfo(PageParam<Object> pageParam, String searchText);

    /**
     * 根据ID获取邀约协作组
     * @param id 主键ID
     * @return 邀约协作组实体
     */
    ProjectMakeFriendsInvitationGroup getById(Long id);

    /**
     * 保存邀约协作组
     * @param entity 邀约协作组实体
     * @return 保存后的实体
     */
    ProjectMakeFriendsInvitationGroup save(ProjectMakeFriendsInvitationGroup entity);

    /**
     * 删除邀约协作组
     * @param id 主键ID
     */
    void deleteById(Long id);

    /**
     * 获取当前用户创建的邀约协作组列表（分页）
     * @param pageParam 分页参数
     * @param searchText 搜索文本（按组名搜索）
     * @return 分页结果
     */
    Page<ProjectMakeFriendsInvitationGroup> getMyList(PageParam<Object> pageParam, String searchText);

    /**
     * 获取当前用户创建或作为协作人的邀约协作组列表（包含创建人信息）
     * @param pageParam 分页参数
     * @param searchText 搜索文本（按组名搜索）
     * @param sortType 排序类型（probability-概率排序，time-时间排序）
     * @return 分页结果
     */
    Page<ProjectMakeFriendsInvitationGroupWithCreatorDTO> getMyCreatedOrCollaboratedListWithCreatorInfo(PageParam<Object> pageParam, String searchText, String sortType);

    /**
     * 创建邀约协作组并添加创建者为成员
     * @param groupName 组名
     * @param description 描述
     * @param userAccountIds 初始成员用户ID列表（包含创建者）
     * @param probability 初始概率值
     * @return 创建的邀约协作组
     */
    ProjectMakeFriendsInvitationGroup createGroupWithMembers(String groupName, String description, List<Long> userAccountIds, BigDecimal probability);

    /**
     * 创建新的共同邀约人组（快速创建）
     * @param userAccountIds 成员用户ID列表
     * @return 创建的邀约协作组
     */
    ProjectMakeFriendsInvitationGroup createNewInvitationGroup(List<Long> userAccountIds);

    /**
     * 创建新的共同邀约人组（带项目名称和概率）
     * @param groupName 项目名称
     * @param usersWithProbability 用户及其概率信息列表
     * @param defaultParticipationStatus 默认参与状态
     * @return 创建的邀约协作组
     */
    ProjectMakeFriendsInvitationGroup createNewInvitationGroupWithProbabilityAndName(String groupName, List<UserProbabilityInfo> usersWithProbability, Integer defaultParticipationStatus);

    /**
     * 创建新的共同邀约人组（支持概率和参与状态设置）
     * @param usersWithProbability 用户及其概率信息列表
     * @param defaultParticipationStatus 默认参与状态
     * @return 创建的邀约协作组
     */
    ProjectMakeFriendsInvitationGroup createNewInvitationGroupWithProbability(List<UserProbabilityInfo> usersWithProbability, Integer defaultParticipationStatus);

    /**
     * 根据组概率随机获取用户ID
     * @param invitationGroupId 邀约组ID
     * @return 随机选择的用户ID
     */
    Long getUserIdByGroupProbability(Long invitationGroupId);

    /**
     * 用户概率信息类
     */
    class UserProbabilityInfo {
        private Long userAccountId;
        private BigDecimal probability;

        public UserProbabilityInfo() {}

        public UserProbabilityInfo(Long userAccountId, BigDecimal probability) {
            this.userAccountId = userAccountId;
            this.probability = probability;
        }

        public Long getUserAccountId() { return userAccountId; }
        public void setUserAccountId(Long userAccountId) { this.userAccountId = userAccountId; }
        public BigDecimal getProbability() { return probability; }
        public void setProbability(BigDecimal probability) { this.probability = probability; }
    }
}
