<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>parent</artifactId>
    <version>ARISTOTLE-0.1</version>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>starter-parent</artifactId>
  <version>ARISTOTLE-0.1</version>
  <packaging>pom</packaging>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <modules>
    <module>starter</module>
  </modules>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>global</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>system-all</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>system-setting</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>system-auth</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>system-language</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>system-dict</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>user-all</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>user-account</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>functional-all</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>functional-file</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>functional-chat</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>official-plugin-all</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>official-plugin-functional-translate</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>tool-all</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>tool-currency</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>front-end-manage-all</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>front-end-manage-admin</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>socket</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>socket-websocket</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>fp</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>fp-carousel</artifactId>
        <version>${kantboot.version}</version>
      </dependency>
      <dependency>
        <groupId>com.kantboot</groupId>
        <artifactId>fp-all</artifactId>
        <version>${revision}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
