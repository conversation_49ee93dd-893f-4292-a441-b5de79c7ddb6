<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.kantboot</groupId>
    <artifactId>util</artifactId>
    <version>ARISTOTLE-0.1</version>
  </parent>
  <groupId>com.kantboot</groupId>
  <artifactId>util-data-change</artifactId>
  <version>ARISTOTLE-0.1</version>
  <description>数据变化工具模块，提供数据变化记录功能</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-cache</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-rest</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.kantboot</groupId>
      <artifactId>util-auth</artifactId>
      <version>${revision}</version>
    </dependency>
  </dependencies>
</project>
