<template>
  <view>
    <view
        class="header"
        id="headerInCollaboratorSelect">
      <kt-nav-bar :title="$i18n.zhToGlobal('协作人管理')" :showBack="true"></kt-nav-bar>
    </view>

    <!-- 内容区域 -->
    <view class="content-box">
      <!-- 搜索框 -->
      <view class="box-search">
        <view class="search-box">
          <u-input
              :placeholder="$i18n.zhToGlobal('请输入ID、手机号或姓名搜索')"
              @confirm="searchUsers"
              v-model="searchKeyword"
              @input="onSearchInput">
            <template slot="suffix">
              <button @tap="searchUsers" class="search-btn" :disabled="!searchKeyword.trim()">搜索</button>
            </template>
          </u-input>
        </view>
      </view>

      <!-- 用户列表区域 -->
      <view class="user-list-section">
        <view v-if="userList.length === 0 && !isLoading && searchKeyword" class="empty-tip">
          <text>{{ $i18n.zhToGlobal('暂无数据') }}</text>
        </view>

        <view v-if="!searchKeyword" class="search-tip">
          <text>{{ $i18n.zhToGlobal('请输入关键词搜索用户') }}</text>
        </view>

        <!-- 用户卡片列表 -->
        <view class="user-item" v-for="user in userList" :key="user.id">
          <view class="user-card">
            <kt-user-info-card :user-info="user"></kt-user-info-card>
            <view class="user-actions">
              <view class="user-id">ID: {{ user.id }}</view>
              <view class="user-phone" v-if="user.phone">{{ user.phoneAreaCode }} {{ user.phone }}</view>
              <view v-if="user.isCollaborator" class="collaborator-status">
                {{ user.permissionLevel === 1 ? $i18n.zhToGlobal('查看权限') : $i18n.zhToGlobal('管理权限') }}
              </view>
              <view class="action-buttons">
                <view v-if="user.isCollaborator" class="action-btn remove-btn" @click="removeCollaborator(user)">
                  {{ $i18n.zhToGlobal('移除') }}
                </view>
                <view v-else class="action-btn add-btn" @click="addCollaborator(user)">
                  {{ $i18n.zhToGlobal('添加') }}
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <u-loading-icon v-if="isLoading" mode="circle" size="50rpx"></u-loading-icon>
        <view v-if="isBottom && userList.length > 0" class="load-more-tip">
          {{ $i18n.zhToGlobal('没有更多了') }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import KtAvatar from "@/uni_modules/kantboot/components/kt-avatar/kt-avatar.vue";

export default {
  components: {
    KtAvatar
  },
  data() {
    return {
      clazz: {
        container: this.$kt.style.toggleClass("container"),
      },
      invitationGroupId: null, // 邀约组ID
      searchKeyword: '', // 搜索关键词
      userList: [], // 用户列表
      isLoading: false, // 是否加载中
      isBottom: false, // 是否到底部
      pageNum: 1,
      pageSize: 20
    }
  },
  onLoad(options) {
    // 获取传入的邀约组ID
    if (options.invitationGroupId) {
      this.invitationGroupId = options.invitationGroupId;
      this.initData();
    } else {
      uni.showToast({
        title: this.$i18n.zhToGlobal('参数错误'),
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  onReachBottom() {
    this.loadMore();
  },
  methods: {
    // 初始化数据
    initData() {
      // 直接加载用户列表，不需要单独加载协作者列表
      this.getUserList();
    },

    // 获取用户列表
    getUserList(isRefresh = false) {
      if (this.isLoading) return;

      // 如果没有搜索关键词，清空列表并返回
      if (!this.searchKeyword.trim()) {
        this.userList = [];
        this.isBottom = false;
        return;
      }

      this.isLoading = true;

      if (isRefresh) {
        this.pageNum = 1;
        this.isBottom = false;
        this.userList = [];
      }

      this.$request.post('/project-make-friends-web/invitationGroupCollaborator/getUserListForCollaborator', {
        data: {
          invitationGroupId: this.invitationGroupId,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          searchText: this.searchKeyword.trim()
        }
      }).then(res => {
        if (res.state === 2000) {
          const newData = res.data || [];

          if (isRefresh) {
            this.userList = newData;
          } else {
            this.userList = [...this.userList, ...newData];
          }

          if (newData.length < this.pageSize) {
            this.isBottom = true;
          }
        } else {
          uni.showToast({
            title: res.msg || this.$i18n.zhToGlobal('获取用户列表失败'),
            icon: 'none'
          });
        }
        this.isLoading = false;
      }).catch(err => {
        console.error('获取用户列表失败:', err);
        uni.showToast({
          title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
          icon: 'none'
        });
        this.isLoading = false;
      });
    },

    // 搜索输入
    onSearchInput(e) {
      this.searchKeyword = e.detail.value;
      // 防抖搜索
      this.debounceSearch();
    },

    // 搜索用户
    searchUsers() {
      this.getUserList(true);
    },

    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.searchUsers();
      }, 500);
    },

    // 加载更多
    loadMore() {
      if (this.isLoading || this.isBottom || !this.searchKeyword.trim()) return;

      this.pageNum++;
      this.getUserList();
    },

    // 添加协作人
    addCollaborator(user) {
      uni.showModal({
        title: this.$i18n.zhToGlobal('确认添加'),
        content: this.$i18n.zhToGlobal('确定要将此用户添加为协作人吗？'),
        success: (res) => {
          if (res.confirm) {
            this.$request.post('/project-make-friends-web/invitationGroupCollaborator/grantCollaboratorPermission', {
              data: {
                invitationGroupId: this.invitationGroupId,
                userAccountId: user.id,
                permissionLevel: 2, // 默认管理权限
                remark: '通过协作人管理页面添加'
              }
            }).then(response => {
              if (response.state === 2000) {
                uni.showToast({
                  title: this.$i18n.zhToGlobal('添加协作人成功'),
                  icon: 'success'
                });

                // 刷新数据
                this.refreshData();

                // 触发父页面事件
                const eventChannel = this.getOpenerEventChannel();
                if (eventChannel) {
                  eventChannel.emit('collaboratorAddSuccess', {
                    user: user,
                    result: response.data
                  });
                }
              } else {
                uni.showToast({
                  title: response.msg || this.$i18n.zhToGlobal('添加协作人失败'),
                  icon: 'none'
                });
              }
            }).catch(err => {
              console.error('添加协作人失败:', err);
              uni.showToast({
                title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
                icon: 'none'
              });
            });
          }
        }
      });
    },

    // 移除协作人
    removeCollaborator(user) {
      uni.showModal({
        title: this.$i18n.zhToGlobal('确认移除'),
        content: this.$i18n.zhToGlobal('确定要移除此协作人吗？'),
        success: (res) => {
          if (res.confirm) {
            this.$request.post('/project-make-friends-web/invitationGroupCollaborator/revokeCollaboratorPermission', {
              data: {
                invitationGroupId: this.invitationGroupId,
                userAccountId: user.id
              }
            }).then(response => {
              if (response.state === 2000) {
                uni.showToast({
                  title: this.$i18n.zhToGlobal('移除协作人成功'),
                  icon: 'success'
                });

                // 刷新数据
                this.refreshData();

                // 触发父页面事件
                const eventChannel = this.getOpenerEventChannel();
                if (eventChannel) {
                  eventChannel.emit('collaboratorAddSuccess', {
                    user: user,
                    result: response.data
                  });
                }
              } else {
                uni.showToast({
                  title: response.msg || this.$i18n.zhToGlobal('移除协作人失败'),
                  icon: 'none'
                });
              }
            }).catch(err => {
              console.error('移除协作人失败:', err);
              uni.showToast({
                title: this.$i18n.zhToGlobal('网络异常，请稍后重试'),
                icon: 'none'
              });
            });
          }
        }
      });
    },

    // 刷新数据
    refreshData() {
      this.getUserList(true);
    }
  }
}
</script>

<style lang="scss" scoped>
.back {
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: #F0F0F0;
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
}

.header-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 999;
}

.content-box {
  padding-bottom: 140rpx;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 搜索框样式 - 与user-select.vue一致 */
.box-search {
  width: calc(100% - 40rpx);
  background-color: #F0F0F0;
  box-sizing: border-box;
  border-radius: 10rpx;
  margin: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 8rpx;
  padding: 10rpx;
}

/* 搜索按钮样式 */
.search-btn {
  background-color: #07C160;
  height: 30px;
  line-height: 30px;
  color: #ffffff;

  &[disabled] {
    background-color: #cccccc;
    color: #ffffff;
    opacity: 0.6;
  }
}

/* 用户列表区域 */
.user-list-section {
  padding: 0 20rpx;

  .empty-tip, .search-tip {
    text-align: center;
    padding: 100rpx 20rpx;
    color: #999999;
    font-size: 28rpx;
  }

  .user-item {
    background-color: #FFFFFF;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    &:active {
      opacity: 0.8;
    }

    .user-card {
      position: relative;

      .user-actions {
        margin-top: 15rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;

        .user-id, .user-phone {
          font-size: 26rpx;
          color: #666666;
          margin-bottom: 5rpx;
        }

        .collaborator-status {
          font-size: 22rpx;
          color: #007AFF;
          background-color: #E3F2FD;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
          display: inline-block;
          margin-bottom: 10rpx;
        }

        .action-buttons {
          margin-top: 10rpx;
          width: 100%;
          display: flex;
          justify-content: flex-end;

          .action-btn {
            padding: 12rpx 20rpx;
            border-radius: 15rpx;
            font-size: 24rpx;
            font-weight: bold;

            &.add-btn {
              background-color: #07C160;
              color: #FFFFFF;
            }

            &.remove-btn {
              background-color: #FF4444;
              color: #FFFFFF;
            }

            &:active {
              opacity: 0.7;
            }
          }
        }
      }
    }
  }

  .load-more-tip {
    text-align: center;
    color: #999999;
    font-size: 24rpx;
    padding: 20rpx;
  }
}

/* 暗黑模式适配 */
.container-mode-color-scheme-dark {
  .back {
    background-color: #1A1A1A;
  }

  .header-box {
    background-color: #2A2A2A;
  }

  .box-search .search-box,
  .user-item {
    background-color: #2A2A2A;
    border: 1rpx solid #333333;
  }

  .user-id, .user-phone {
    color: #AAAAAA;
  }

  .empty-tip, .search-tip, .load-more-tip {
    color: #AAAAAA;
  }
}
</style>